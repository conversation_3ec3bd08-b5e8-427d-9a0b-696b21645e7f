package dataAll.equip.define
{
   import com.sounto.net.EmbedMethod;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.equip.define.strengthen.EquipStrengthenAll;
   import dataAll.pro.PropertyArrayDefine;
   
   public class EquipDefineGroup
   {
      
      public var obj:Object = {};
      
      public var fashionObj:Object = {};
      
      public var rangeArr:Array = [];
      
      private var swfUrlObj:Object = {};
      
      public var fatherObj:Object = {};
      
      private var rareFatherArr:Array = [];
      
      public var blackFatherArr:Array = [];
      
      private var blackProMaxObj:Object = {};
      
      public var strengthen:EquipStrengthenAll = new EquipStrengthenAll();
      
      public function EquipDefineGroup()
      {
         super();
      }
      
      public function inImageData_byXML(xml0:XML, rangeB0:Boolean = true) : void
      {
         var i:* = undefined;
         var father_list2:XMLList = null;
         var n:* = undefined;
         var range_d0:EquipRangeDefine = null;
         var xml2:XML = null;
         var img_list3:XMLList = null;
         var father_name0:String = null;
         var swf_name0:String = null;
         var f0:EquipFatherDefine = null;
         var m:* = undefined;
         var d0:EquipDefine = null;
         var gather_list2:XMLList = xml0.gather;
         var fatherIndex0:int = 0;
         for(i in gather_list2)
         {
            father_list2 = gather_list2[i].father;
            if(rangeB0)
            {
               range_d0 = new EquipRangeDefine();
               range_d0.inData_byXML(gather_list2[i]);
               this.rangeArr.push(range_d0);
            }
            for(n in father_list2)
            {
               xml2 = father_list2[n];
               img_list3 = xml2.image;
               father_name0 = xml2.@name;
               swf_name0 = xml2.@imgSwf;
               if(swf_name0 != "")
               {
                  this.addSwfInObj(swf_name0,father_name0);
               }
               f0 = new EquipFatherDefine();
               f0.inData_byXML(xml2,fatherIndex0);
               fatherIndex0++;
               this.fatherObj[f0.name] = f0;
               if(f0.color == EquipColor.RED && f0.rareDropType != "")
               {
                  this.rareFatherArr.push(f0);
               }
               else if(f0.color == EquipColor.BLACK)
               {
                  this.blackFatherArr.push(f0);
               }
               for(m in img_list3)
               {
                  d0 = new EquipDefine();
                  d0.inData_byXML(img_list3[m],father_name0);
                  this.obj[d0.name] = d0;
                  if(rangeB0)
                  {
                     range_d0[d0.type].push(d0);
                  }
                  f0.addPart(d0);
                  if(f0.color == EquipColor.BLACK)
                  {
                     this.addBlackDefine(d0);
                  }
               }
            }
         }
      }
      
      public function inFashionData_byXML(xml0:XML) : void
      {
         var i:* = undefined;
         var father_list2:XMLList = null;
         var n:* = undefined;
         var fx0:XML = null;
         var father_name0:String = null;
         var img_xl0:XMLList = null;
         var m:* = undefined;
         var d0:EquipDefine = null;
         var gather_list2:XMLList = xml0.gather;
         var fatherIndex0:int = 0;
         for(i in gather_list2)
         {
            father_list2 = gather_list2[i].father;
            for(n in father_list2)
            {
               fx0 = father_list2[n];
               father_name0 = fx0.@name;
               img_xl0 = fx0.image;
               for(m in img_xl0)
               {
                  d0 = new EquipDefine();
                  d0.inFashion_byXML(img_xl0[m]);
                  this.obj[d0.name] = d0;
                  this.fashionObj[d0.name] = d0;
                  this.addSwfInObj(d0.imgSwf,d0.name);
               }
            }
         }
      }
      
      private function addBlackDefine(d0:EquipDefine) : void
      {
         var n:* = undefined;
         var obj0:Object = d0.getOtherObj();
         for(n in obj0)
         {
            if(!this.blackProMaxObj.hasOwnProperty(n))
            {
               this.blackProMaxObj[n] = 0;
            }
            if(obj0[n] > this.blackProMaxObj[n])
            {
               this.blackProMaxObj[n] = obj0[n];
            }
         }
      }
      
      public function getDefine(name0:String) : EquipDefine
      {
         return this.obj[name0];
      }
      
      public function getFashionDefine(name0:String) : EquipDefine
      {
         return this.fashionObj[name0];
      }
      
      public function getFashionDefineByCn(cn0:String) : EquipDefine
      {
         var d0:EquipDefine = null;
         for each(d0 in this.fashionObj)
         {
            if(d0.cnName == cn0)
            {
               return d0;
            }
         }
         return null;
      }
      
      public function getFatherDefine(name0:String) : EquipFatherDefine
      {
         return this.fatherObj[name0];
      }
      
      private function addSwfInObj(swf_name0:String, father_name0:String) : void
      {
         if(!this.swfUrlObj.hasOwnProperty(swf_name0) && swf_name0 != "")
         {
            this.swfUrlObj[swf_name0] = father_name0;
         }
      }
      
      public function getEmbledText() : String
      {
         var swfName:* = undefined;
         var name0:String = null;
         var swfUrl0:String = null;
         var s0:String = null;
         var all0:String = "";
         for(swfName in this.swfUrlObj)
         {
            name0 = this.swfUrlObj[swfName];
            swfUrl0 = "equip/" + swfName + ".swf";
            s0 = EmbedMethod.getSwfEmbed(name0,swfUrl0);
            all0 += s0 + "\n";
         }
         return all0;
      }
      
      public function getStrengthenMustNum(lv0:int, nowStrengthenLv0:int) : int
      {
         var v0:int = this.strengthen.getPropertyValue("mustNum",nowStrengthenLv0 + 1) + 1;
         if(lv0 > 80)
         {
            v0 += 4;
         }
         return v0;
      }
      
      public function getSumStrengthenMustNum(lv0:int, nowStrengthenLv0:int, startStrengthenLv0:int, successRateB0:Boolean) : Number
      {
         var n0:Number = NaN;
         var successRate0:Number = NaN;
         var num0:Number = 0;
         for(var i:int = startStrengthenLv0; i <= nowStrengthenLv0; i++)
         {
            n0 = this.getStrengthenMustNum(lv0,i);
            if(successRateB0)
            {
               successRate0 = this.strengthen.getPropertyValue("successRate",i + 1);
               n0 = int(n0 / successRate0);
            }
            num0 += n0;
         }
         return num0;
      }
      
      public function getRareDropEquipFatherDefine(lv0:int, proB0:Boolean) : EquipFatherDefine
      {
         var f0:EquipFatherDefine = null;
         var index0:int = 0;
         if(lv0 < 70)
         {
            return null;
         }
         var arr0:Array = [];
         var proArr0:Array = [];
         for each(f0 in this.rareFatherArr)
         {
            if(lv0 >= f0.rareDropLv)
            {
               arr0.push(f0);
               proArr0.push(RareDropType.getDropMul(f0.rareDropType,lv0));
            }
         }
         if(arr0.length == 0)
         {
            return null;
         }
         if(proB0)
         {
            index0 = ComMethod.getPro_byArrSum(proArr0);
            return arr0[index0];
         }
         return arr0[int(Math.random() * arr0.length)];
      }
      
      public function getBlackEquipDefineByLv(lv0:int) : EquipDefine
      {
         var arr0:Array = this.getBlackEquipDefineArr(lv0);
         return arr0[int(Math.random() * arr0.length)];
      }
      
      public function getBlackEquipDefineArr(lv0:int) : Array
      {
         var f0:EquipFatherDefine = null;
         var d0:EquipDefine = null;
         var arr0:Array = [];
         for each(f0 in this.blackFatherArr)
         {
            for each(d0 in f0.partObj)
            {
               if(d0.blackDropLevelArr.indexOf(lv0 + "") >= 0 && lv0 >= 81)
               {
                  arr0.push(d0);
               }
            }
         }
         return arr0;
      }
      
      public function getAllBlackEquipNameArr() : Array
      {
         var f0:EquipFatherDefine = null;
         var d0:EquipDefine = null;
         var arr0:Array = [];
         for each(f0 in this.blackFatherArr)
         {
            for each(d0 in f0.partObj)
            {
               if(d0.blackDropLevelArr.length > 0)
               {
                  if(d0.blackDropLevelArr[0] < 100)
                  {
                     arr0.push(d0.name);
                  }
               }
            }
         }
         return arr0;
      }
      
      public function getRandomBlackEquip(type0:String) : EquipDefine
      {
         var f0:EquipFatherDefine = null;
         var d0:EquipDefine = null;
         var arr0:Array = [];
         for each(f0 in this.blackFatherArr)
         {
            d0 = f0.partObj[type0];
            if(d0.itemsLevel < 86)
            {
               arr0.push(d0);
            }
         }
         return arr0[int(Math.random() * arr0.length)];
      }
      
      public function getBlackProMax(name0:String) : Number
      {
         var pd0:PropertyArrayDefine = null;
         if(!this.blackProMaxObj.hasOwnProperty(name0))
         {
            return 0;
         }
         pd0 = Gaming.defineGroup.getPropertyArrayDefine(name0);
         return pd0.getBlackValueRange(this.blackProMaxObj[name0])[1];
      }
      
      public function blackTest() : void
      {
         var lv0:int = 0;
         var d0:EquipDefine = null;
         for(var i:int = 0; i < 150; i++)
         {
            lv0 = 76 + int(i / 10);
            d0 = this.getBlackEquipDefineByLv(lv0);
            if(!d0)
            {
               trace(lv0 + "：");
            }
            else
            {
               trace(lv0 + "：" + d0.cnName);
            }
         }
      }
      
      public function getIconNum() : int
      {
         var d0:EquipDefine = null;
         var obj0:Object = {};
         var num0:int = 0;
         for each(d0 in this.obj)
         {
            if(!obj0.hasOwnProperty(d0.iconLabel))
            {
               obj0[d0.iconLabel] = 0;
               num0++;
            }
         }
         return num0;
      }
   }
}

