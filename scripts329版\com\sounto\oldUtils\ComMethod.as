package com.sounto.oldUtils
{
   import com.common.data.Line2;
   import com.common.text.TextWay;
   import com.sounto.math.IDRect;
   import com.sounto.utils.ClassProperty;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.geom.ColorTransform;
   import flash.geom.Matrix;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.text.StyleSheet;
   import flash.utils.ByteArray;
   
   public class ComMethod
   {
      
      public static const WAN:String = "万";
      
      public static const YI:String = "亿";
      
      public function ComMethod()
      {
         super();
      }
      
      public static function clearAllChildren(sp0:Sprite) : *
      {
         var num0:int = sp0.numChildren;
         for(var n:int = 0; n < num0; n++)
         {
            sp0.removeChildAt(0);
         }
      }
      
      public static function getTimeStr(time0:Number) : String
      {
         var _hour:int = int(time0 / 3600);
         var _miu:int = int((time0 - _hour * 3600) / 60);
         var _sec:int = int(time0 % 60);
         return to2(_hour) + ":" + to2(_miu) + ":" + to2(_sec);
      }
      
      public static function getTimeStrTwo(time0:Number, unitB0:Boolean = false) : String
      {
         var _miu:int = int(time0 / 60);
         var _sec:int = int(time0 % 60);
         if(unitB0)
         {
            return to2(_miu) + "分" + to2(_sec) + "秒";
         }
         return to2(_miu) + ":" + to2(_sec);
      }
      
      public static function color(str:*, _color1:String = "#999999", size0:int = 0) : String
      {
         var sizeStr0:String = size0 == 0 ? "" : "size=\'" + size0 + "\' ";
         return "<font " + sizeStr0 + "color=\'" + _color1 + "\'>" + str + "</font>";
      }
      
      public static function yellow(str:String) : String
      {
         return color(str,"#FFFF00");
      }
      
      public static function red(str:String) : String
      {
         return color(str,"#FF0000");
      }
      
      public static function mustColor(now0:int, max0:int, PTB0:Boolean = false, yesColor0:String = "#00FF00", noColor0:String = "#FF0000") : String
      {
         var str0:String = color(now0 + "",now0 < max0 ? noColor0 : yesColor0) + "/" + max0;
         if(PTB0)
         {
            str0 = "(" + str0 + ")";
         }
         return str0;
      }
      
      public static function dropColor(now0:Number, max0:Number, nowColor0:String = "#0099FF", maxColor0:String = "#00FF00") : String
      {
         var s0:String = "";
         if(now0 < max0)
         {
            if(nowColor0 != "")
            {
               s0 = color(now0 + "",nowColor0);
            }
            else
            {
               s0 = now0 + "";
            }
            s0 = "<b>" + s0 + "</b>/" + max0;
         }
         else
         {
            s0 = color("<b>" + now0 + "</b>/" + max0,maxColor0);
         }
         return s0;
      }
      
      public static function colorMustNum(now0:Number, must0:Number) : String
      {
         if(must0 <= now0)
         {
            return color(now0 + "","#00FF00") + "/" + must0;
         }
         return color(now0 + "","#FF6D48") + "/" + must0;
      }
      
      public static function moreRed(now0:Number, max0:Number) : String
      {
         return mustColor(now0,max0,false,"#FF0000","#00FF00");
      }
      
      public static function moreGreen(now0:Number, max0:Number) : String
      {
         return mustColor(now0,max0,false,"#00FF00","#999999");
      }
      
      public static function colorEnoughNum(v0:Number) : String
      {
         return color(v0 + "",v0 > 0 ? "#00FF00" : "#FF0000");
      }
      
      public static function noEnough(str0:String = "(条件不足)") : String
      {
         return color(str0,"#FF0000");
      }
      
      public static function link(str0:String, linkLabel0:String) : String
      {
         return "<a href=\"event:" + linkLabel0 + "\">" + str0 + "</a>";
      }
      
      public static function linkUrl(str0:String, linkUrl0:String) : String
      {
         return "<a href=\"" + linkUrl0 + "\" target=\"blank\">" + str0 + "</a>";
      }
      
      public static function getLinkCss(linkColor0:String, overColor0:String) : StyleSheet
      {
         var style:StyleSheet = new StyleSheet();
         style.setStyle("a:link",{
            "color":linkColor0,
            "textDecoration":"underline"
         });
         style.setStyle("a:hover",{
            "color":overColor0,
            "textDecoration":"underline"
         });
         return style;
      }
      
      public static function getPro_byArr(arr0:Array) : int
      {
         var n:* = undefined;
         var ran0:Number = Math.random();
         var num0:Number = 0;
         for(n in arr0)
         {
            num0 += Number(arr0[n]);
            if(ran0 < num0)
            {
               return n;
            }
         }
         return arr0.length - 1;
      }
      
      public static function getPro_byArrNo(arr0:Array) : int
      {
         var n:* = undefined;
         var ran0:Number = Math.random();
         var num0:Number = 0;
         for(n in arr0)
         {
            num0 += Number(arr0[n]);
            if(ran0 < num0)
            {
               return n;
            }
         }
         return -1;
      }
      
      public static function getPro_byArrSum(arr0:Array) : int
      {
         var n:* = undefined;
         var num0:Number = NaN;
         var ran0:Number = Math.random();
         var max0:Number = 0;
         for(n in arr0)
         {
            max0 += Number(arr0[n]);
         }
         num0 = 0;
         for(n in arr0)
         {
            num0 += Number(arr0[n]);
            if(ran0 < num0 / max0)
            {
               return n;
            }
         }
         return arr0.length - 1;
      }
      
      public static function getRandomIntInTwo(min0:int, max0:int) : int
      {
         var v0:int = Math.round(min0 + (max0 - min0) * Math.random());
         if(v0 < min0)
         {
            v0 = min0;
         }
         if(v0 > max0)
         {
            v0 = max0;
         }
         return v0;
      }
      
      public static function firstUpperCase(str0:String) : String
      {
         var s0:String = str0.charAt();
         s0 = s0.toUpperCase();
         return s0 + str0.substr(1);
      }
      
      public static function firstLowerCase(str0:String) : String
      {
         var s0:String = str0.charAt();
         s0 = s0.toLowerCase();
         return s0 + str0.substr(1);
      }
      
      public static function getDropNum(random0:Number) : int
      {
         var num0:int = int(random0);
         var ran0:Number = random0 - num0;
         if(Math.random() < ran0)
         {
            num0++;
         }
         return num0;
      }
      
      public static function deductArr(arr0:Array, arr1:Array) : Array
      {
         var n:* = undefined;
         var arr2:Array = [];
         for(n in arr0)
         {
            if(arr1.indexOf(arr0[n]) == -1)
            {
               arr2.push(arr0[n]);
            }
         }
         return arr2;
      }
      
      public static function copyArray(arr0:Array) : Array
      {
         var data0:ByteArray = new ByteArray();
         data0.writeObject(arr0);
         data0.position = 0;
         return data0.readObject();
      }
      
      public static function clearArrayRepeatString(arr0:Array) : Array
      {
         var n:* = undefined;
         var str0:String = null;
         var arr1:Array = [];
         for(n in arr0)
         {
            str0 = arr0[n];
            if(arr1.indexOf(str0) == -1)
            {
               arr1.push(str0);
            }
         }
         return arr1;
      }
      
      public static function getRandomArray(arr0:Array, num0:int, repeatB0:Boolean = false) : Array
      {
         var newArr0:Array = null;
         var s_arr0:Array = null;
         var i:int = 0;
         var index0:int = 0;
         var len0:int = int(arr0.length);
         if(!repeatB0 && num0 >= len0)
         {
            return arr0.concat([]);
         }
         newArr0 = [];
         s_arr0 = arr0.concat([]);
         for(i = 0; i < num0; i++)
         {
            index0 = Math.random() * s_arr0.length;
            newArr0.push(s_arr0[index0]);
            if(!repeatB0)
            {
               s_arr0.splice(index0,1);
            }
         }
         return newArr0;
      }
      
      public static function sortNumberArray(arr0:Array) : void
      {
         arr0.sort(sortNumberArray_fun);
      }
      
      private static function sortNumberArray_fun(a0:Number, b0:Number) : int
      {
         if(a0 < b0)
         {
            return -1;
         }
         if(a0 == b0)
         {
            return 0;
         }
         return 1;
      }
      
      public static function sortRandomArray(arr0:Array) : void
      {
         arr0.sort(sortRandomArray_fun);
      }
      
      private static function sortRandomArray_fun(a:*, b:*) : int
      {
         return Math.random() > 0.5 ? 1 : -1;
      }
      
      public static function addNoRepeatInArr(arr0:Array, v0:*) : void
      {
         if(arr0.indexOf(v0) == -1)
         {
            arr0.push(v0);
         }
      }
      
      public static function addNoRepeatArrInArr(arr0:Array, arr2:Array) : void
      {
         var v0:* = undefined;
         for each(v0 in arr2)
         {
            if(arr0.indexOf(v0) == -1)
            {
               arr0.push(v0);
            }
         }
      }
      
      public static function delOneInArr(arr0:Array, v0:*) : Boolean
      {
         var i0:int = int(arr0.indexOf(v0));
         if(i0 == -1)
         {
            return false;
         }
         arr0.splice(i0,1);
         return true;
      }
      
      public static function coverObj(targetObj0:Object, obj2:Object) : void
      {
         var n:* = undefined;
         for(n in targetObj0)
         {
            if(obj2.hasOwnProperty(n))
            {
               targetObj0[n] = obj2[n];
            }
         }
      }
      
      public static function fixedObj(obj0:Object, obj2:Object) : Object
      {
         var n:* = undefined;
         var targetObj0:Object = ClassProperty.copyObj(obj0);
         for(n in obj2)
         {
            if(!targetObj0.hasOwnProperty(n))
            {
               targetObj0[n] = 0;
            }
            targetObj0[n] += obj2[n];
         }
         return targetObj0;
      }
      
      public static function getObjElementNum(obj0:Object) : int
      {
         var n:* = undefined;
         var num0:int = 0;
         for(n in obj0)
         {
            num0++;
         }
         return num0;
      }
      
      public static function addNumInObj(obj0:Object, name0:String, num0:int = 1) : void
      {
         if(!obj0.hasOwnProperty(name0))
         {
            obj0[name0] = 0;
         }
         obj0[name0] += num0;
      }
      
      public static function stringToNumberArr(str0:String, splitString0:String = ",") : Array
      {
         var n:* = undefined;
         var arr0:Array = str0.split(splitString0);
         for(n in arr0)
         {
            arr0[n] = Number(arr0[n]);
         }
         return arr0;
      }
      
      public static function stringToRangeArr(str0:String) : Array
      {
         var n:* = undefined;
         var arr1:Array = null;
         var i:* = undefined;
         var arr0:Array = str0.split(",");
         for(n in arr0)
         {
            str0 = arr0[n];
            arr1 = str0.split("~");
            for(i in arr1)
            {
               arr1[i] = Number(arr1[i]);
            }
            arr0[n] = arr1;
         }
         return arr0;
      }
      
      public static function stringToStringArr(str0:String) : Array
      {
         var n:* = undefined;
         var arr0:Array = str0.split(",");
         for(n in arr0)
         {
            arr0[n] = TextWay.toHan2(arr0[n]);
         }
         return arr0;
      }
      
      public static function xmlToArr(xml:XMLList) : Array
      {
         var n:* = undefined;
         var arr:Array = [];
         for(n in xml)
         {
            arr.push(String(xml[n]));
         }
         return arr;
      }
      
      public static function xmlToRectArr(xml:XMLList) : Array
      {
         var n:* = undefined;
         var arr:Array = [];
         for(n in xml)
         {
            arr.push(getRect(String(xml[n])));
         }
         return arr;
      }
      
      public static function xmlToIDRectArr(xml:XMLList) : Array
      {
         var n:* = undefined;
         var arr:Array = [];
         for(n in xml)
         {
            arr.push(getIDRect(String(xml[n])));
         }
         return arr;
      }
      
      public static function getPerCompareStr(v0:Number, max0:Number, enoughB0:Boolean = true) : String
      {
         var color0:String = "#FF0000";
         if(enoughB0)
         {
            color0 = v0 >= max0 ? "#FFFF00" : "#FF0000";
         }
         else
         {
            color0 = v0 >= max0 ? "#FF0000" : "#FFFF00";
         }
         return ComMethod.color(Math.round(v0 * 100) + "/" + TextWay.numberToPer(max0,0),color0);
      }
      
      public static function getRect(str:String) : Rectangle
      {
         if(str == "")
         {
            return null;
         }
         var hitr:Array = str.split(",");
         var hurtRect:Rectangle = new Rectangle();
         if(hitr.length >= 4)
         {
            hurtRect.x = Number(hitr[0]);
            hurtRect.y = Number(hitr[1]);
            hurtRect.width = Number(hitr[2]);
            hurtRect.height = Number(hitr[3]);
         }
         return hurtRect;
      }
      
      public static function getIDRect(str:String) : IDRect
      {
         var rect0:IDRect = new IDRect();
         rect0.inData(getRect(str));
         return rect0;
      }
      
      public static function getLine(str:String) : Line2
      {
         var hitr:Array = str.split(",");
         var l0:Line2 = new Line2();
         l0.x = Number(hitr[0]);
         l0.y = Number(hitr[1]);
         l0.ra = Number(hitr[2]) / 180 * Math.PI;
         l0.w = Number(hitr[3]);
         if(hitr.length >= 5)
         {
            l0.len = Number(hitr[4]);
         }
         return l0;
      }
      
      public static function getPoint(str:String) : Point
      {
         var shootp:Array = str.split(",");
         var shootPoint:Point = new Point();
         if(shootp.length >= 2)
         {
            shootPoint.x = shootp[0];
            shootPoint.y = shootp[1];
         }
         return shootPoint;
      }
      
      public static function getDecimalPoint1(num0:Number) : int
      {
         return int(num0 * 10) / 10;
      }
      
      public static function to2(num:int) : String
      {
         if(num < 10)
         {
            return "0" + num;
         }
         return String(num);
      }
      
      public static function toFixed(v0:Number, num0:int) : Number
      {
         return Number(v0.toFixed(num0));
      }
      
      public static function toInt(v0:Number, num0:int) : Number
      {
         var n0:Number = Math.pow(10,num0);
         return Number(Number(Math.ceil(v0 / n0) * n0).toFixed(0));
      }
      
      public static function flipRect_Y(rect:Rectangle) : Rectangle
      {
         var r0:Rectangle = rect.clone();
         r0.x = -(r0.width + r0.x);
         return r0;
      }
      
      public static function gotoIndex(i0:int, len0:int) : int
      {
         if(i0 < 0)
         {
            i0 = 0;
         }
         if(i0 > len0 - 1)
         {
            i0 = len0 - 1;
         }
         return i0;
      }
      
      public static function getBmp(mc:DisplayObject, rectOrBounds:Boolean = true, scale:Number = 1) : BitmapData
      {
         var rect0:Rectangle = null;
         if(rectOrBounds)
         {
            rect0 = mc.getRect(mc);
         }
         else
         {
            rect0 = mc.getBounds(mc);
         }
         rect0.x *= scale;
         rect0.y *= scale;
         rect0.width *= scale;
         rect0.height *= scale;
         if(rect0.width == 0)
         {
            rect0.width = 1;
         }
         if(rect0.height == 0)
         {
            rect0.height = 1;
         }
         var mix:Matrix = new Matrix();
         mix.tx = -rect0.x / scale;
         mix.ty = -rect0.y / scale;
         mix.scale(scale,scale);
         var bmp0:BitmapData = new BitmapData(rect0.width,rect0.height,true,0);
         bmp0.draw(mc,mix);
         return bmp0;
      }
      
      public static function numberToSmall(v0:Number, color0:String = "#00FF00", max0:Number = 99999, yiMax0:Number = 999999999, fixed0:int = 0) : String
      {
         var unit0:String = null;
         var v2:Number = NaN;
         var str0:String = "";
         if(v0 > max0)
         {
            unit0 = WAN;
            v2 = Math.ceil(v0 / 10000);
            if(fixed0 > 0 && v2 < 100)
            {
               v2 = Number(Number(v0 / 10000).toFixed(fixed0));
            }
            if(v0 > yiMax0)
            {
               unit0 = YI;
               v2 = Math.ceil(v0 / 100000000);
               if(fixed0 > 0 && v2 < 100)
               {
                  v2 = Number(Number(v0 / 100000000).toFixed(fixed0));
               }
            }
            if(color0 != "")
            {
               unit0 = color(unit0,color0);
            }
            str0 = v2 + unit0;
         }
         else
         {
            str0 = String(v0);
         }
         return str0;
      }
      
      public static function randomObj(obj0:Object) : Object
      {
         var arr0:Array = objToArr(obj0);
         return arr0[int(Math.random() * arr0.length)];
      }
      
      public static function objToArr(obj0:Object) : Array
      {
         var b0:Object = null;
         var arr0:Array = [];
         for each(b0 in obj0)
         {
            arr0.push(b0);
         }
         return arr0;
      }
      
      public static function sortNameArrByObjAndStr(obj0:Object, jsonStr0:String) : Array
      {
         var n:* = undefined;
         var nameArr0:Array = null;
         var b2:Object = null;
         var f0:int = 0;
         var b0:Object = null;
         var objArr0:Array = [];
         for(n in obj0)
         {
            f0 = int(jsonStr0.indexOf(n));
            b0 = {
               "name":n,
               "index":f0
            };
            objArr0.push(b0);
         }
         objArr0.sort(sortNameArrByObjAndStr_fun);
         nameArr0 = [];
         for each(b2 in objArr0)
         {
            nameArr0.push(b2.name);
         }
         return nameArr0;
      }
      
      private static function sortNameArrByObjAndStr_fun(b1:Object, b2:Object) : int
      {
         if(b1.index < b2.index)
         {
            return -1;
         }
         if(b1.index < b2.index)
         {
            return 1;
         }
         return 0;
      }
      
      public static function haveDecimalNum(v0:Number) : int
      {
         var str0:String = String(v0);
         var f0:int = int(str0.indexOf("."));
         if(f0 == -1)
         {
            return 0;
         }
         return str0.length - f0 - 1;
      }
      
      public static function inDataColorF(c1:ColorTransform, c2:ColorTransform) : void
      {
         c1.alphaMultiplier = c2.alphaMultiplier;
         c1.alphaOffset = c2.alphaOffset;
         c1.blueMultiplier = c2.blueMultiplier;
         c1.blueOffset = c2.blueOffset;
         c1.greenMultiplier = c2.greenMultiplier;
         c1.greenOffset = c2.greenOffset;
         c1.redMultiplier = c2.redMultiplier;
         c1.redOffset = c2.redOffset;
      }
      
      public static function clearColorF(c1:ColorTransform) : void
      {
         c1.alphaMultiplier = 1;
         c1.alphaOffset = 0;
         c1.blueMultiplier = 1;
         c1.blueOffset = 0;
         c1.greenMultiplier = 1;
         c1.greenOffset = 0;
         c1.redMultiplier = 1;
         c1.redOffset = 0;
      }
   }
}

