package dataAll.body.define
{
   import dataAll._app.wilder.define.WilderDefine;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll.body.attack.ElementShell;
   import dataAll.level.define.LevelDefine;
   import dataAll.skill.define.SkillDefine;
   
   public class BodyDefineGroup
   {
      
      private var fatherArrObj:Object = {};
      
      public var arr:Array = [];
      
      public var obj:Object = {};
      
      public var cnObj:Object = {};
      
      private var indexObj:Object = {};
      
      public function BodyDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML, systemType0:String = "normal") : void
      {
         var i:* = undefined;
         var bodyXML0:* = undefined;
         var father0:String = null;
         var n:* = undefined;
         var x1:XML = null;
         var fixed0:String = null;
         var d0:NormalBodyDefine = null;
         var fixedD0:NormalBodyDefine = null;
         var arr0:Array = this.arr;
         var fatherXML0:XMLList = xml0.father;
         for(i in fatherXML0)
         {
            bodyXML0 = fatherXML0[i].body;
            father0 = fatherXML0[i].@name;
            for(n in bodyXML0)
            {
               x1 = bodyXML0[n];
               fixed0 = x1.@fixed;
               if(fixed0 == "")
               {
                  d0 = systemType0 == BodySystemType.DEFINE_HERO ? new HeroDefine() : new NormalBodyDefine();
                  d0.inData_byXML(x1,systemType0,father0);
               }
               else
               {
                  fixedD0 = this.getDefine(fixed0);
                  if(fixedD0 is NormalBodyDefine)
                  {
                     d0 = fixedD0.cloneByXml();
                  }
                  else
                  {
                     d0 = systemType0 == BodySystemType.DEFINE_HERO ? new HeroDefine() : new NormalBodyDefine();
                  }
                  d0.inFastData_byXML(x1,systemType0,father0);
               }
               this.arr.push(d0);
               if(!this.obj.hasOwnProperty(systemType0))
               {
                  this.obj[systemType0] = {};
               }
               this.obj[systemType0][d0.name] = d0;
               if(father0 != "pet")
               {
                  this.cnObj[d0.cnName] = d0;
               }
               this.addInFatherArr(d0,father0);
            }
         }
      }
      
      private function addInFatherArr(d0:NormalBodyDefine, father0:String) : void
      {
         if(!this.fatherArrObj.hasOwnProperty(father0))
         {
            this.fatherArrObj[father0] = [];
         }
         this.fatherArrObj[father0].push(d0);
      }
      
      public function dealMovieLink() : void
      {
         var n:* = undefined;
         var d0:HeroDefine = null;
         var d2:HeroDefine = null;
         var obj0:Object = this.obj[BodySystemType.DEFINE_HERO];
         for(n in obj0)
         {
            d0 = obj0[n];
            if(d0.movieLink != "")
            {
               d2 = this.getHeroDefine(d0.movieLink);
               if(!d2)
               {
                  INIT.showError("找不movieLink指定的定义：" + d0.movieLink);
               }
               d0.movieDefineArr = d2.movieDefineArr;
               d0.imgArr = d2.imgArr;
               d0.hurtObj = d2.hurtObj;
               d0.hurtArr = d2.hurtArr;
            }
         }
         this.dealIndex();
      }
      
      public function getDefine(name0:String) : NormalBodyDefine
      {
         var d0:NormalBodyDefine = this.obj[BodySystemType.DEFINE_NORMAL][name0];
         if(!d0)
         {
            d0 = this.getHeroDefine(name0);
         }
         return d0;
      }
      
      public function getDefineByIndex(i0:int) : NormalBodyDefine
      {
         return this.indexObj[i0];
      }
      
      public function getHeroDefine(name0:String) : HeroDefine
      {
         return this.obj[BodySystemType.DEFINE_HERO][name0];
      }
      
      public function getCnDefine(cnName0:String) : NormalBodyDefine
      {
         return this.cnObj[cnName0];
      }
      
      public function getCnArrByNameArr(nameArr0:Array) : Array
      {
         var name0:String = null;
         var d0:NormalBodyDefine = null;
         var cnArr0:Array = [];
         for each(name0 in nameArr0)
         {
            d0 = this.getDefine(name0);
            if(Boolean(d0))
            {
               cnArr0.push(d0.cnName);
            }
         }
         return cnArr0;
      }
      
      public function getRoleCnArrByNameArr(nameArr0:Array) : Array
      {
         var name0:String = null;
         var d0:NormalBodyDefine = null;
         var cnArr0:Array = [];
         for each(name0 in nameArr0)
         {
            d0 = this.getDefine(name0);
            if(Boolean(d0))
            {
               cnArr0.push(d0.getRoleCn());
            }
         }
         return cnArr0;
      }
      
      public function getDefineCopy(name0:String) : NormalBodyDefine
      {
         var d0:NormalBodyDefine = this.getDefine(name0);
         if(Boolean(d0))
         {
            return d0.clone();
         }
         return null;
      }
      
      public function getArrByFather(father0:String) : Array
      {
         return this.fatherArrObj[father0];
      }
      
      public function getNormalEnemyNameArr(type0:String, maxLv0:int = 999) : Array
      {
         var d0:NormalBodyDefine = null;
         var bb0:Boolean = false;
         var arr0:Array = this.getArrByFather(BodyFather.enemy);
         var nameArr0:Array = [];
         for each(d0 in arr0)
         {
            bb0 = false;
            if(d0.extraDropArmsB)
            {
               bb0 = type0 == "boss1" || type0 == "boss2";
            }
            else if(type0 == "normal")
            {
               bb0 = !(d0 is HeroDefine);
            }
            else if(type0 == "shoot")
            {
               bb0 = d0 is HeroDefine;
            }
            if(d0.showLevel > maxLv0)
            {
               bb0 = false;
            }
            if(bb0)
            {
               nameArr0.push(d0.name);
            }
         }
         return nameArr0;
      }
      
      public function cnArrToNameArr(arr0:Array) : Array
      {
         var cnName0:String = null;
         var arr2:Array = [];
         for each(cnName0 in arr0)
         {
            arr2.push(this.cnToName(cnName0));
         }
         return arr2;
      }
      
      public function nameArrToCnArr(arr0:Array) : Array
      {
         var name0:String = null;
         var arr2:Array = [];
         for each(name0 in arr0)
         {
            arr2.push(this.nameToCn(name0));
         }
         return arr2;
      }
      
      public function cnToName(cnName0:String) : String
      {
         var d0:NormalBodyDefine = this.getCnDefine(cnName0);
         return d0.name;
      }
      
      public function nameToCn(name0:String) : String
      {
         var d0:NormalBodyDefine = this.getDefine(name0);
         return d0.cnName;
      }
      
      public function afterDeal() : void
      {
         this.dealIndex();
         this.worldMapInMap();
         this.wilderInMap();
      }
      
      private function dealIndex() : void
      {
         var d0:NormalBodyDefine = null;
         var i0:int = 0;
         for each(d0 in this.arr)
         {
            i0++;
            d0.index = i0;
            this.indexObj[i0] = d0;
         }
      }
      
      private function worldMapInMap() : void
      {
         var d0:WorldMapDefine = null;
         var levelName0:String = null;
         var levelD0:LevelDefine = null;
         var bossArr0:Array = null;
         var boss0:String = null;
         var bossD0:NormalBodyDefine = null;
         var arr0:Array = Gaming.defineGroup.worldMap.getShowMapArr();
         for each(d0 in arr0)
         {
            levelName0 = d0.getLevelName();
            levelD0 = Gaming.defineGroup.level.getDefine(levelName0);
            if(Boolean(levelD0))
            {
               bossArr0 = levelD0.unitG.getBossNameArr();
               for each(boss0 in bossArr0)
               {
                  bossD0 = Gaming.defineGroup.body.getDefine(boss0);
                  if(bossD0.map == "")
                  {
                     bossD0.map = d0.name;
                  }
               }
            }
         }
      }
      
      private function wilderInMap() : void
      {
         var d0:WilderDefine = null;
         var bossD0:NormalBodyDefine = null;
         var mapd0:WorldMapDefine = null;
         var arr0:Array = Gaming.defineGroup.wilder.arr;
         for each(d0 in arr0)
         {
            bossD0 = Gaming.defineGroup.body.getDefine(d0.name);
            if(Boolean(bossD0))
            {
               if(bossD0.map == "")
               {
                  bossD0.map = d0.scene;
                  mapd0 = Gaming.defineGroup.worldMap.getDefine(bossD0.map);
               }
            }
         }
      }
      
      public function traceAllEnemyCanCopy() : void
      {
         var d0:NormalBodyDefine = null;
         var skillArr0:Array = null;
         var arr2:Array = null;
         var skill0:String = null;
         var sd0:SkillDefine = null;
         var arr0:Array = this.getArrByFather(BodyFather.enemy);
         arr0 = arr0.concat(this.getArrByFather(BodyFather.wilder));
         for each(d0 in arr0)
         {
            skillArr0 = d0.getEditSkillArr();
            arr2 = [];
            for each(skill0 in skillArr0)
            {
               sd0 = Gaming.defineGroup.skill.getDefine(skill0);
               if(sd0.canCopyB())
               {
                  arr2.push(sd0.cnName);
               }
            }
            if(arr2.length > 0)
            {
               trace("【" + d0.cnName + "】" + "--------------------" + arr2);
            }
         }
      }
      
      public function testSwfName() : void
      {
         var d0:NormalBodyDefine = null;
         trace("--------------------------------------------");
         for each(d0 in this.arr)
         {
            if(d0.getSwfName() != d0.name)
            {
               trace(d0.name + "!=" + d0.getSwfName() + "    " + d0.cnName);
            }
         }
         trace("--------------------------------------------");
      }
      
      public function test() : void
      {
         this.testTrace("normal");
         this.testTrace("shoot");
         this.testTrace("boss1");
         this.testTrace("boss2");
      }
      
      private function testTrace(type0:String) : void
      {
         var arr0:Array = this.getNormalEnemyNameArr(type0);
         var cnArr0:Array = this.nameArrToCnArr(arr0);
         trace(type0 + "：" + cnArr0);
      }
      
      public function imgCheck() : void
      {
         var d0:NormalBodyDefine = null;
         var arr0:Array = null;
         var str0:String = null;
         var allStr0:String = "";
         for each(d0 in this.arr)
         {
            arr0 = d0.getAllImageUrl();
            str0 = Gaming.swfLoaderManager.checkFatherArr(arr0,[d0.name]);
            trace("检查【" + d0.cnName + "】" + arr0);
            if(str0 != "")
            {
               allStr0 += "【" + d0.name + "】" + str0 + "\n";
            }
         }
         if(allStr0 != "")
         {
            trace("？？？？？？？？？？？？？？？？？？？？？？？？？？？？？？？？？？？\n");
            trace(allStr0);
            trace("？？？？？？？？？？？？？？？？？？？？？？？？？？？？？？？？？？？");
         }
      }
      
      public function sortSpeed() : void
      {
         var d0:NormalBodyDefine = null;
         var i:int = 0;
         var arr0:Array = null;
         var v0:int = 0;
         var obj0:Object = {};
         for each(d0 in this.arr)
         {
            if(obj0.hasOwnProperty(d0.maxVx) == false)
            {
               obj0[d0.maxVx] = [];
            }
            arr0 = obj0[d0.maxVx];
            arr0.push(d0.cnName);
         }
         for(i = 0; i < 25; i++)
         {
            v0 = i + 1;
            INIT.tempTrace(v0 + "  ：  " + obj0[v0]);
         }
      }
      
      public function countShell() : void
      {
         var d0:NormalBodyDefine = null;
         var n:* = undefined;
         var s0:String = null;
         var sarr0:Array = null;
         var cn0:String = null;
         var obj0:Object = {};
         for each(d0 in this.arr)
         {
            s0 = d0.shell;
            if(ElementShell.arr.indexOf(s0) == -1)
            {
               INIT.showErrorMust("不存在这个外壳类型：" + s0);
            }
            sarr0 = null;
            if(obj0.hasOwnProperty(s0) == false)
            {
               sarr0 = [];
               obj0[s0] = sarr0;
            }
            else
            {
               sarr0 = obj0[s0];
            }
            sarr0.push(d0.cnName);
         }
         for(n in obj0)
         {
            trace(n + "------------------------");
            sarr0 = obj0[n];
            for each(cn0 in sarr0)
            {
               trace(cn0);
            }
         }
      }
   }
}

