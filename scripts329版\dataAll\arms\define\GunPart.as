package dataAll.arms.define
{
   public class GunPart
   {
      
      public static const texture:String = "texture";
      
      public static const body:String = "body";
      
      public static const barrel:String = "barrel";
      
      public static const grip:String = "grip";
      
      public static const bullet:String = "bullet";
      
      public static const stock:String = "stock";
      
      public static const glass:String = "glass";
      
      public static const shoot:String = "shoot";
      
      public static const left_hand:String = "left_hand";
      
      public static const right_hand:String = "right_hand";
      
      public static const bullet_hand:String = "bullet_hand";
      
      public static const ARR:Array = [texture,body,barrel,grip,bullet,stock,glass];
      
      public static const PART_ARR:Array = [glass,body,barrel,grip,stock,bullet];
      
      public static const DEFINE_ARR:Array = [body,barrel,grip,stock,bullet];
      
      public static const POINT_ARR:Array = [barrel,grip,bullet,stock,glass,shoot,left_hand,right_hand,bullet_hand];
      
      public function GunPart()
      {
         super();
      }
      
      public static function getPartUrlObj(imgUrl0:String) : Object
      {
         var n:* = undefined;
         var pro0:String = null;
         var label0:String = null;
         var arr0:Array = imgUrl0.split("_");
         var obj0:Object = {};
         for(n in arr0)
         {
            pro0 = ARR[n];
            label0 = arr0[n];
            obj0[pro0] = label0.replace("$","/");
         }
         return obj0;
      }
   }
}

