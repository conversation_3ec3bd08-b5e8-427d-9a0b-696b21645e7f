package dataAll.drop.define
{
   public class DropColorAll
   {
      
      public var arr:Array = [];
      
      public var obj:Object = {};
      
      public function DropColorAll()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var dg0:DropColorDefineGroup = null;
         var xml_list0:XMLList = xml0.father;
         for(n in xml_list0)
         {
            dg0 = new DropColorDefineGroup();
            dg0.inData_byXML(xml_list0[n]);
            this.arr.push(dg0);
            this.obj[dg0.name] = dg0;
         }
      }
      
      public function getByName(name0:String) : DropColorDefineGroup
      {
         return this.obj[name0];
      }
      
      public function afterDeal() : void
      {
      }
   }
}

