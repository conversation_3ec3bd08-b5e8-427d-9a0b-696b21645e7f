package dataAll.items.creator
{
   import UI.bag.ItemsGripBtnListCtrl;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._player.PlayerData;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.save.EquipSave;
   import dataAll.gift.GiftAddit;
   import dataAll.things.ThingsData;
   import dataAll.things.define.ThingsDefine;
   
   public class ItemsComposeCtrl
   {
      
      private static var tempTip:String = "";
      
      public function ItemsComposeCtrl()
      {
         super();
      }
      
      public static function getTip(d0:ThingsDefine, pd0:PlayerData) : String
      {
         var equipDa0:EquipData = GiftAddit.getEquipDataByName(d0.name,pd0);
         if(Boolean(equipDa0))
         {
            return getEquipTip(equipDa0,d0,pd0);
         }
         return "";
      }
      
      private static function getEquipTip(da0:EquipData, d0:ThingsDefine, pd0:PlayerData) : String
      {
         var equipD0:EquipDefine = da0.save.getDefine();
         var num0:int = pd0.thingsBag.getThingsNum(d0.name);
         var must0:int = da0.save.getComposeMustNum();
         var str0:String = "可合成" + EquipType.getCnName(da0.save.partType) + "：<yellow " + (equipD0.haveLevelB() ? da0.save.getTrueLevel() + "级" : "") + da0.getCnName() + "/>";
         str0 += "\n所需碎片：" + ComMethod.colorMustNum(num0,must0) + "个";
         str0 += "\n<blue 获得方式：/>\n" + d0.description + "\n";
         return str0 + da0.getGatherTip(null,"chip");
      }
      
      public static function compose(da0:ThingsData) : void
      {
         var d0:ThingsDefine = null;
         var pd0:PlayerData = null;
         var bb0:Boolean = false;
         var equipS0:EquipSave = null;
         var loginB0:Boolean = Gaming.PG.loginData.isLoginByJS();
         if(!loginB0)
         {
            Gaming.uiGroup.alertBox.showError("您的账号已经退出登录，无法进行此操作。");
         }
         else
         {
            d0 = da0.save.getDefine();
            pd0 = da0.playerData;
            bb0 = false;
            equipS0 = GiftAddit.getEquipSaveByName(d0.name,pd0.level);
            if(equipS0 is EquipSave)
            {
               bb0 = composeEquip(equipS0,da0);
            }
            if(bb0)
            {
               ItemsGripBtnListCtrl.fleshAllBy(pd0.thingsBag);
               tempTip = "成功合成" + equipS0.cnName + "！";
               affterCompose();
            }
         }
      }
      
      private static function composeEquip(equipS0:EquipSave, da0:ThingsData) : Boolean
      {
         var d0:ThingsDefine = da0.save.getDefine();
         var pd0:PlayerData = da0.playerData;
         var num0:int = da0.save.nowNum;
         var must0:int = equipS0.getComposeMustNum();
         if(num0 < must0)
         {
            Gaming.uiGroup.alertBox.showError("碎片个数不足" + must0 + "个，无法合成" + equipS0.cnName + "。");
            return false;
         }
         if(pd0.equipBag.getSpaceSiteNum() <= 0)
         {
            Gaming.uiGroup.alertBox.showError("背包空位不足，无法合成装备。");
            return false;
         }
         pd0.thingsBag.useThings(d0.name,must0);
         pd0.equipBag.addSave(equipS0);
         return true;
      }
      
      private static function affterCompose(v:* = null) : void
      {
         Gaming.uiGroup.alertBox.showSuccess(tempTip);
      }
   }
}

