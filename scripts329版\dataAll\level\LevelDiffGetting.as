package dataAll.level
{
   import com.sounto.utils.ArrayMethod;
   import dataAll._app.space.SpaceDiff;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import gameAll.drop.bodyDrop.MadBodyDrop;
   
   public class LevelDiffGetting
   {
      
      private static var lifeDiff:Array = [1,1.3,2,3.5,6.4,10,20].concat([40,80,160]);
      
      private static var dpsDiff:Array = [1,1.3,2,3.5,4,5,6].concat([7,8,9]);
      
      public static var coinMul:Array = [1,1.3,1.8,2.5,4,6,8];
      
      public static var expMul:Array = [1,1.3,1.8,2.5,4,6,8];
      
      public static var superSkillNum:Array = [2,2,3,4,5,6,7];
      
      public static var superAddNum:Array = [0,0,1,1,2,3,3];
      
      private static var trueBossPro:Array = [0.02,0.03,0.05,0.1,0.1,0.1];
      
      private static var treasurePro:Array = [0.02,0.03,0.05,0.1,0.1,0.15,0.2];
      
      private static var nameArr:Array = ["简单","普通","困难","超难","神难","炼狱","地狱"].concat(["地狱二","地狱三","地狱四"]);
      
      private static var xingGu_lifeDiff:Array = [4,10,22,50,100,200,400,800];
      
      private static var xingGu_dpsDiff:Array = [0.4,0.7,1,1.2,1.5,2,2.5,3];
      
      private static var after_lifeDiff:Array = [2,5,11,25,50,100,200,400,600,800];
      
      private static var after_dpsDiff:Array = dpsDiff;
      
      private static var demon_lifeDiff:Array = [1.5,4,10,20,40,80,140,150];
      
      private static var beforeDemon_lifeDiff:Array = [1.5,4,10,20,40,80,140];
      
      private static var demon_dpsDiff:Array = [1.3,2,3,4,5,6,7,8];
      
      private static var demon_superSkillNum:Array = [4,4,5,5,6,6,6,6];
      
      private static var demon_superAddNum:Array = [2];
      
      private static var demon_nameArr:Array = ["修罗一","修罗二","修罗三","修罗四","修罗五","修罗六","修罗七","修罗八","修罗九","修罗十"];
      
      public static var demon8B:Boolean = true;
      
      public function LevelDiffGetting()
      {
         super();
      }
      
      private static function isSpace(mapName0:String) : Boolean
      {
         var d0:WorldMapDefine = Gaming.defineGroup.worldMap.getDefine(mapName0);
         if(Boolean(d0))
         {
            return d0.isSpaceB();
         }
         return false;
      }
      
      public static function getDiffOpen(diff0:int, mapMode0:String, baseLv0:int, mapName0:String) : Boolean
      {
         var maxDiff0:int = 0;
         var d0:WorldMapDefine = Gaming.defineGroup.worldMap.getDefine(mapName0);
         if(mapName0 == MadBodyDrop.XingGu)
         {
            maxDiff0 = MadBodyDrop.getOpenMaxDiff();
         }
         else if(d0.diffMax > 0)
         {
            maxDiff0 = d0.diffMax;
         }
         else if(baseLv0 > 95)
         {
            maxDiff0 = 6 + 3;
         }
         else if(baseLv0 > 85)
         {
            maxDiff0 = 6 + 3;
         }
         else if(baseLv0 > 80)
         {
            maxDiff0 = 6 + 1;
         }
         else if(baseLv0 > 50)
         {
            maxDiff0 = 4;
         }
         else
         {
            maxDiff0 = 3;
         }
         if(diff0 <= maxDiff0)
         {
            return true;
         }
         return false;
      }
      
      public static function getSweeping(diff0:int, mapMode0:String, lv0:int, diffUnlock0:int, mapName0:String) : Boolean
      {
         var d0:WorldMapDefine = null;
         var maxDiff0:int = 0;
         var sweepingB0:Boolean = false;
         if(mapMode0 == MapMode.REGULAR)
         {
            d0 = Gaming.defineGroup.worldMap.getDefine(mapName0);
            maxDiff0 = 6;
            if(d0.sweepMax != 0)
            {
               maxDiff0 = d0.sweepMax;
            }
            if(diff0 <= maxDiff0 && diff0 < diffUnlock0)
            {
               sweepingB0 = true;
            }
         }
         return sweepingB0;
      }
      
      public static function getHellTwo() : int
      {
         return 7;
      }
      
      public static function getLv96() : int
      {
         return 96;
      }
      
      public static function getDropColor(diff0:int, mapMode0:String) : String
      {
         if(mapMode0 == MapMode.DEMON)
         {
            if(diff0 < 4)
            {
               return "demon_diff_0";
            }
            return "demon_diff_4";
         }
         if(diff0 > 6)
         {
            diff0 = 6;
         }
         return "diff_" + diff0;
      }
      
      public static function getDemonDiff8() : int
      {
         return 7;
      }
      
      public static function getDemonDiffLen() : int
      {
         if(demon8B)
         {
            return demon_lifeDiff.length;
         }
         return beforeDemon_lifeDiff.length;
      }
      
      public static function getDemonDiffMax() : int
      {
         return getDemonDiffLen() - 1;
      }
      
      public static function getDiff() : int
      {
         return Gaming.LG.nowLevel.dat.diff;
      }
      
      private static function getMapMode() : String
      {
         return Gaming.LG.mapMode;
      }
      
      private static function getMapName() : String
      {
         return Gaming.LG.getNowWorldMapName();
      }
      
      public static function getLifeDiffMul() : Number
      {
         return getLifeDiffBy(getDiff(),getMapMode(),getMapName());
      }
      
      public static function getDpsDiffMul() : Number
      {
         return getDpsDiffBy(getDiff(),getMapMode(),getMapName());
      }
      
      public static function getCoinMul() : Number
      {
         return getCoinMulBy(getDiff(),getMapMode());
      }
      
      public static function getExpMul() : Number
      {
         return getExpMulBy(getDiff(),getMapMode(),getMapName());
      }
      
      public static function getSuperSkillNum() : int
      {
         var arr0:Array = superSkillNum;
         if(getMapMode() == MapMode.DEMON)
         {
            arr0 = demon_superSkillNum;
         }
         return ArrayMethod.getElementLimit(arr0,getDiff()) as int;
      }
      
      public static function getSuperAddNum() : int
      {
         var arr0:Array = superAddNum;
         if(getMapMode() == MapMode.DEMON)
         {
            arr0 = demon_superAddNum;
         }
         return ArrayMethod.getElementLimit(arr0,getDiff()) as int;
      }
      
      public static function getDiffLen() : int
      {
         return lifeDiff.length;
      }
      
      public static function getDiffLenByMode(mapMode0:String) : int
      {
         if(mapMode0 == MapMode.DEMON)
         {
            return getDemonDiffLen();
         }
         return getDiffLen();
      }
      
      public static function getDiffMax() : int
      {
         return lifeDiff.length - 1;
      }
      
      public static function getCnName(diff0:int, mapMode0:String) : String
      {
         var arr0:Array = nameArr;
         if(mapMode0 == MapMode.DEMON)
         {
            arr0 = demon_nameArr;
         }
         return ArrayMethod.getElementLimit(arr0,diff0) as String;
      }
      
      public static function getTrueBossPro(diff0:int, mapMode0:String) : Number
      {
         var arr0:Array = trueBossPro;
         if(mapMode0 == MapMode.DEMON)
         {
            return 0;
         }
         return ArrayMethod.getElementLimit(arr0,diff0) as Number;
      }
      
      public static function getTreasurePro(diff0:int, mapMode0:String) : Number
      {
         var arr0:Array = treasurePro;
         if(mapMode0 == MapMode.DEMON)
         {
            return 0;
         }
         return ArrayMethod.getElementLimit(arr0,diff0) as Number;
      }
      
      public static function getLifeDiffBy(diff0:int, mapMode0:String, mapName0:String) : Number
      {
         var d0:WorldMapDefine = null;
         var arr0:Array = lifeDiff;
         if(mapMode0 == MapMode.DEMON)
         {
            arr0 = demon_lifeDiff;
            if(demon8B)
            {
               arr0 = demon_lifeDiff;
            }
            else
            {
               arr0 = beforeDemon_lifeDiff;
            }
         }
         else if(mapName0 == MadBodyDrop.XingGu)
         {
            arr0 = xingGu_lifeDiff;
         }
         else if(isSpace(mapName0))
         {
            arr0 = SpaceDiff.lifeDiff;
         }
         else
         {
            d0 = Gaming.defineGroup.worldMap.getDefine(mapName0);
            if(Boolean(d0))
            {
               if(d0.isAfterXingGuB())
               {
                  arr0 = after_lifeDiff;
               }
            }
         }
         return ArrayMethod.getElementLimit(arr0,diff0) as Number;
      }
      
      public static function getDpsDiffBy(diff0:int, mapMode0:String, mapName0:String) : Number
      {
         var d0:WorldMapDefine = null;
         var arr0:Array = dpsDiff;
         if(mapMode0 == MapMode.DEMON)
         {
            arr0 = demon_dpsDiff;
         }
         else if(mapName0 == MadBodyDrop.XingGu)
         {
            arr0 = xingGu_dpsDiff;
         }
         else if(isSpace(mapName0))
         {
            arr0 = SpaceDiff.dpsDiff;
         }
         else
         {
            d0 = Gaming.defineGroup.worldMap.getDefine(mapName0);
            if(Boolean(d0))
            {
               if(d0.isAfterXingGuB())
               {
                  arr0 = after_dpsDiff;
               }
            }
         }
         return ArrayMethod.getElementLimit(arr0,diff0) as Number;
      }
      
      public static function getCoinMulBy(diff0:int, mapMode0:String) : Number
      {
         var arr0:Array = coinMul;
         if(mapMode0 == MapMode.DEMON)
         {
            return 8;
         }
         var v0:Number = ArrayMethod.getElementLimit(arr0,diff0) as Number;
         if(v0 > 10)
         {
            v0 = 10;
         }
         return v0;
      }
      
      public static function getExpMulBy(diff0:int, mapMode0:String, mapName0:String) : Number
      {
         var arr0:Array = expMul;
         if(mapMode0 == MapMode.DEMON)
         {
            return 8;
         }
         if(isSpace(mapName0))
         {
            arr0 = SpaceDiff.expMul;
         }
         var v0:Number = ArrayMethod.getElementLimit(arr0,diff0) as Number;
         if(v0 > 10)
         {
            v0 = 10;
         }
         return v0;
      }
      
      public static function getLotteryMul(diff0:int, mapMode0:String) : Number
      {
         var arr0:Array = [1,1.3,1.7,2.5,3.5,4.5,5.5,6.5];
         if(mapMode0 == MapMode.DEMON)
         {
            return 3;
         }
         return ArrayMethod.getElementLimit(arr0,diff0) as Number;
      }
      
      public static function getNewLotteryMul(diff0:int, mapMode0:String) : Number
      {
         var arr0:Array = [0.2,0.3,0.5,0.7,1,1.5,2,2.5];
         if(mapMode0 == MapMode.DEMON)
         {
            return 1;
         }
         return ArrayMethod.getElementLimit(arr0,diff0) as Number;
      }
   }
}

