package dataAll.equip.vehicle
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.NumberMethod;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipType;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.pro.PropertyArrayDefine;
   import dataAll.skill.define.SkillDescrip;
   import dataAll.ui.GatherColor;
   import dataAll.ui.text.ProTipType;
   
   public class VehicleDefine extends EquipDefine
   {
      
      public static var pro_arr:Array = [];
      
      public static const chestArr:Array = ["FlyDragonAir","Thunder","SaberTigerCar"];
      
      private static const layerToUpArr:Array = ["Titans","Prophet"];
      
      private static const havePetAddArr:Array = ["Diggers"];
      
      public var rideLabel:String = "";
      
      public var vehicleType:String = "";
      
      public var defineType:String = "shoot";
      
      public var bodyLabel:String = "";
      
      public var isFitB:Boolean = false;
      
      public var canComposeB:Boolean = false;
      
      public var onceB:Boolean = false;
      
      public var shopB:Boolean = false;
      
      public var mustSightMul:Number = 0;
      
      private var _mustCash:String = "";
      
      private var _lifeMul:String = "";
      
      private var _attackMul:String = "";
      
      public var attackBulletNum:int = 1;
      
      public var attackActionLabel:String = "";
      
      private var _duration:String = "";
      
      private var _cd:String = "";
      
      public var specialInfoArr:Array = [];
      
      public var evolutionLabel:String = "";
      
      public var evolutionLv:int = 1;
      
      private var baseLabel:String = "";
      
      private var _beforeDefine:VehicleDefine = null;
      
      public function VehicleDefine()
      {
         super();
         this.lifeMul = 1;
         this.duration = 30;
         this.cd = 120;
         this.attackMul = 1;
         this.mustCash = 100;
      }
      
      public static function meOrEvolutionIndexOf(n0:String, arr0:Array) : int
      {
         var name0:String = null;
         var d0:VehicleDefine = null;
         var index0:int = 0;
         for each(name0 in arr0)
         {
            d0 = Gaming.defineGroup.vehicle.getDefine(name0);
            if(d0.panIsMeOrEvolutionB(n0))
            {
               return index0;
            }
            index0++;
         }
         return -1;
      }
      
      public static function clearMeOrEvolutionInArr(n0:String, arr0:Array) : int
      {
         var index0:int = meOrEvolutionIndexOf(n0,arr0);
         if(index0 >= 0)
         {
            arr0.splice(index0,1);
         }
         return index0;
      }
      
      public function set mustCash(v0:Number) : void
      {
         this._mustCash = TextWay.toCode32(String(v0));
      }
      
      public function get mustCash() : Number
      {
         return Number(TextWay.getText32(this._mustCash));
      }
      
      public function set lifeMul(v0:Number) : void
      {
         this._lifeMul = TextWay.toCode32(String(v0));
      }
      
      public function get lifeMul() : Number
      {
         return Number(TextWay.getText32(this._lifeMul));
      }
      
      public function set attackMul(v0:Number) : void
      {
         this._attackMul = TextWay.toCode32(String(v0));
      }
      
      public function get attackMul() : Number
      {
         return Number(TextWay.getText32(this._attackMul));
      }
      
      public function set duration(v0:Number) : void
      {
         this._duration = TextWay.toCode32(String(v0));
      }
      
      public function get duration() : Number
      {
         return Number(TextWay.getText32(this._duration));
      }
      
      public function set cd(v0:Number) : void
      {
         this._cd = TextWay.toCode32(String(v0));
      }
      
      public function get cd() : Number
      {
         return Number(TextWay.getText32(this._cd));
      }
      
      override public function inData_byXML(xml0:XML, father0:String) : void
      {
         father = father0;
         name = xml0.@name;
         cnName = xml0.@cnName;
         this.evolutionLabel = xml0.@evolutionLabel;
         this.rideLabel = xml0.@rideLabel;
         ClassProperty.inData_byXML(this,xml0,pro_arr);
         if(this.bodyLabel == "")
         {
            this.bodyLabel = name;
         }
         if(iconLabel == "")
         {
            iconLabel = "ThingsIcon/" + name;
         }
         type = EquipType.VEHICLE;
         if(this.vehicleType == "")
         {
            this.vehicleType = father;
         }
         if(this.evolutionLv >= 2)
         {
            if(this.vehicleType == "car")
            {
               skillArr = skillArr.concat(["crazy_vehicle","murderous_vehicle"]);
            }
            else
            {
               skillArr = skillArr.concat(["crazy_vehicle"]);
            }
            skillArr = skillArr.concat(["alloyShell","strongStrong","shockWave","meetingGift"]);
         }
      }
      
      public function haveRideB() : Boolean
      {
         return this.rideLabel != "";
      }
      
      public function isChestB() : Boolean
      {
         return chestArr.indexOf(this.getBaseLabel()) >= 0;
      }
      
      public function isShopB() : Boolean
      {
         var base0:VehicleDefine = this.getBaseDefine();
         if(base0 == null)
         {
            base0 = this;
         }
         return base0.shopB;
      }
      
      public function getBullletNum(label0:String) : int
      {
         if(label0 == VehicleAttackLabel.ATTACK)
         {
            return this.attackBulletNum;
         }
         return 1;
      }
      
      public function getMul(type0:String) : Number
      {
         return this.getMulByLabel(type0 + "Mul");
      }
      
      public function getMulByLabel(name0:String) : Number
      {
         var label0:String = name0.substr(0,name0.length - 3);
         if(this.hasOwnProperty(name0))
         {
            return this[name0];
         }
         return 0;
      }
      
      public function getVehicleTypeCn() : String
      {
         if(this.vehicleType == "car")
         {
            return "战车";
         }
         if(this.vehicleType == "aircraft")
         {
            return "飞行器";
         }
         return "";
      }
      
      public function getMustThingsName() : String
      {
         if(name == "BlueMoto")
         {
            return "RedMotoCash";
         }
         if(name == "Thunder")
         {
            return "magicChest";
         }
         if(name == "FlyDragonAir")
         {
            return "dragonChest";
         }
         return name + "Cash";
      }
      
      public function layerToUpB() : Boolean
      {
         if(layerToUpArr.indexOf(this.getBaseLabel()) >= 0)
         {
            return true;
         }
         return false;
      }
      
      public function getSpecialInfo() : String
      {
         if(this.specialInfoArr.length == 0)
         {
            return "";
         }
         return TextWay.mixedStringArr(this.specialInfoArr,1,"\n");
      }
      
      public function getBodyDefine() : NormalBodyDefine
      {
         return Gaming.defineGroup.body.getDefine(this.bodyLabel);
      }
      
      public function getSkillArr() : Array
      {
         return skillArr;
      }
      
      public function getAddSkillArr() : Array
      {
         var before0:VehicleDefine = this.getBeforeDefine();
         if(Boolean(before0))
         {
            return ArrayMethod.deductArr(this.getSkillArr(),before0.getSkillArr());
         }
         return this.getSkillArr();
      }
      
      override public function getSortIndex() : int
      {
         var index0:int = index;
         var d0:VehicleDefine = this.getBaseDefine();
         if(Boolean(d0))
         {
            index0 = d0.index;
         }
         return index0 * 1000 + 99 - this.evolutionLv;
      }
      
      public function getAttackLabelArr() : Array
      {
         var label0:String = null;
         var arr0:Array = VehicleDefineType.getDpsNameArr(this.defineType);
         var newArr0:Array = [];
         for each(label0 in arr0)
         {
            if(this.getMul(label0) > 0)
            {
               newArr0.push(label0);
            }
         }
         return newArr0;
      }
      
      private function haveAttackLabelB(label0:String) : Boolean
      {
         var arr0:Array = this.getAttackLabelArr();
         return arr0.indexOf(label0) >= 0;
      }
      
      private function haveAttackProB(pro0:String) : Boolean
      {
         var type0:String = null;
         var Type0:String = null;
         var arr0:Array = this.getAttackLabelArr();
         for each(type0 in arr0)
         {
            Type0 = ComMethod.firstUpperCase(type0);
            if(pro0.indexOf(type0) >= 0 || pro0.indexOf(Type0) >= 0)
            {
               return true;
            }
         }
         return false;
      }
      
      public function filterProNameArr(arr0:Array) : Array
      {
         var pro0:String = null;
         var newArr0:Array = [];
         for each(pro0 in arr0)
         {
            if(VehicleProper.hurtProArr.indexOf(pro0) >= 0)
            {
               if(this.haveAttackProB(pro0))
               {
                  newArr0.push(pro0);
               }
            }
            else
            {
               newArr0.push(pro0);
            }
         }
         return newArr0;
      }
      
      public function fitlerProCnName(d0:PropertyArrayDefine) : String
      {
         return this.fitlerProCn(d0.cnName);
      }
      
      public function fitlerProCn(cn0:String) : String
      {
         return cn0;
      }
      
      public function getEvoMustThings() : String
      {
         return this.getBaseDefine().getMustThingsName();
      }
      
      public function getBaseDefine() : VehicleDefine
      {
         return Gaming.defineGroup.vehicle.getDefine(this.getBaseLabel());
      }
      
      public function getBaseLabel() : String
      {
         var arr0:Array = null;
         if(this.baseLabel == "")
         {
            arr0 = this.getAllBeforeNameArr();
            if(arr0.length == 0)
            {
               this.baseLabel = name;
            }
            else
            {
               this.baseLabel = arr0[arr0.length - 1];
            }
         }
         return this.baseLabel;
      }
      
      public function getEvolutionDefine() : VehicleDefine
      {
         if(this.canEvolutionB())
         {
            return Gaming.defineGroup.vehicle.getDefine(this.evolutionLabel);
         }
         return null;
      }
      
      public function getBeforeDefine() : VehicleDefine
      {
         if(this.evolutionLv > 1)
         {
            if(this._beforeDefine == null)
            {
               this._beforeDefine = Gaming.defineGroup.vehicle.getBeforeDefine(name);
            }
            return this._beforeDefine;
         }
         return null;
      }
      
      public function canEvolutionB() : Boolean
      {
         return this.evolutionLabel != "";
      }
      
      public function panIsMeOrEvolutionB(n0:String) : Boolean
      {
         var baseName0:String = null;
         var i:int = 0;
         var d0:VehicleDefine = null;
         var e0:String = null;
         if(n0 == name)
         {
            return true;
         }
         baseName0 = name;
         for(i = 0; i < 10; i++)
         {
            d0 = Gaming.defineGroup.vehicle.getDefine(baseName0);
            if(d0 == null)
            {
               return false;
            }
            e0 = d0.evolutionLabel;
            if(e0 == "")
            {
               return false;
            }
            if(e0 == n0)
            {
               return true;
            }
            baseName0 = e0;
         }
         return false;
      }
      
      public function getAllBeforeNameArr() : Array
      {
         var d0:VehicleDefine = null;
         var c0:String = null;
         var arr0:Array = [];
         var baseName0:String = name;
         for(var i:int = 0; i < 10; i++)
         {
            d0 = Gaming.defineGroup.vehicle.getBeforeDefine(baseName0);
            if(!Boolean(d0))
            {
               break;
            }
            c0 = d0.name;
            baseName0 = c0;
            arr0.push(c0);
         }
         return arr0;
      }
      
      override public function getAddObj() : Object
      {
         var obj0:Object = null;
         var dpsMul0:Number = NaN;
         if(_addObj == null)
         {
            obj0 = super.getAddObj();
            if(obj0.hasOwnProperty("dpsAll"))
            {
               dpsMul0 = Number(obj0["dpsAll"]);
               if(this.isShopB() && havePetAddArr.indexOf(this.getBaseLabel()) == -1)
               {
                  obj0["rareGeneDropPro"] = NumberMethod.toFixed(dpsMul0 * 1.3,2);
                  obj0["petBookDropPro"] = NumberMethod.toFixed(dpsMul0 * 0.8,2);
               }
               else if(this.isChestB() || havePetAddArr.indexOf(this.getBaseLabel()) >= 0)
               {
                  obj0["rareGeneDropPro"] = NumberMethod.toFixed(dpsMul0 * 1.1,2);
               }
               else
               {
                  obj0["rareGeneDropPro"] = NumberMethod.toFixed(dpsMul0 * 0.8,2);
               }
            }
            return obj0;
         }
         return super.getAddObj();
      }
      
      override public function getResolveGift() : GiftAddDefineGroup
      {
         var g0:GiftAddDefineGroup = null;
         if(this.canComposeB)
         {
            g0 = new GiftAddDefineGroup();
            g0.addGiftByStr("things;" + this.getMustThingsName() + ";" + this.mustCash);
            return g0;
         }
         return null;
      }
      
      override public function canResolveB() : Boolean
      {
         return this.canComposeB;
      }
      
      public function getPreBulletNameArr() : Array
      {
         return [];
      }
      
      public function getSoundUrlArr() : Array
      {
         return [];
      }
      
      public function test() : void
      {
         if(addObjJson != "")
         {
            this.getAddObj();
         }
      }
      
      public function getSkillGather() : String
      {
         var s0:String = "";
         var skillArr0:Array = this.getSkillArr();
         s0 += ProTipType.getTitleMixed("载具可学技能：");
         return s0 + ("\n" + SkillDescrip.getSkillArrGather(skillArr0,GatherColor.yellowColor,false,false,false,true));
      }
      
      public function getAddSkillGather() : String
      {
         var s0:String = "";
         var skillArr0:Array = this.getAddSkillArr();
         s0 += ProTipType.getTitleMixed("载具新增技能：");
         return s0 + ("\n" + SkillDescrip.getSkillArrGather(skillArr0,GatherColor.yellowColor,false,false,false,true));
      }
   }
}

