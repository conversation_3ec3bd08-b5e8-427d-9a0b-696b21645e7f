package dataAll.equip.weapon
{
   import com.sounto.utils.ClassProperty;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.save.EquipSave;
   
   public class WeaponSave extends EquipSave
   {
      
      public static var pro_arr:Array = [];
      
      public var s:String = "";
      
      public var cf:Boolean = false;
      
      public function WeaponSave()
      {
         super();
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         super.inData_byObj(obj0);
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      override public function inDataByDefine(d0:EquipDefine) : void
      {
         super.inDataByDefine(d0);
         var weaponDefine0:WeaponDefine = d0 as WeaponDefine;
         name = weaponDefine0.name;
         itemsLevel = weaponDefine0.lv;
         skillArr = weaponDefine0.skillArr;
      }
      
      override public function getDataClass() : EquipData
      {
         return new WeaponData();
      }
      
      override public function getDefine() : EquipDefine
      {
         return this.getWeaponDefine();
      }
      
      public function getWeaponDefine() : WeaponDefine
      {
         return Gaming.defineGroup.weapon.getDefine(name);
      }
      
      override public function getTrueObj() : Object
      {
         return obj;
      }
      
      override public function clone() : EquipSave
      {
         var obj0:Object = ClassProperty.copyObj(this);
         var s0:EquipSave = new WeaponSave();
         s0.inData_byObj(obj0);
         return s0;
      }
   }
}

