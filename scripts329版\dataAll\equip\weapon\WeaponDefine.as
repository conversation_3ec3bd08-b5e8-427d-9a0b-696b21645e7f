package dataAll.equip.weapon
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll.equip.creator.EquipPropertyDataCreator;
   import dataAll.equip.creator.EquipSkillCreator;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipType;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.must.define.MustDefine;
   import dataAll.skill.define.SkillDefine;
   
   public class WeaponDefine extends EquipDefine
   {
      
      public static var MAX_LEVEL:int = 2;
      
      public static var pro_arr:Array = [];
      
      private static const dpsAddArr:Array = [1,4,7,10,20,30,40,42,44];
      
      private static const imgIndexArr:Array = [1,1,1,1,2,2,2,3,4];
      
      public var armsType:String = "";
      
      public var weaponType:String = "";
      
      public var baseLabel:String = "";
      
      public var lv:int = 1;
      
      public var rareB:Boolean = false;
      
      public var hitNum:int = 1;
      
      public var actionLabel:String = "";
      
      public var weaponUrl:String = "";
      
      private var skin:String = "";
      
      public var layerIndex:int = 0;
      
      public function WeaponDefine()
      {
         super();
         this.anger = 0;
         this.hurtMul = 0;
         this.proAddLv = 0;
         this.upgradeNum = 0;
         this.angerAddMul = 1;
      }
      
      public function get anger() : Number
      {
         return Math.round(CF.getAttribute("anger"));
      }
      
      public function set anger(v0:Number) : void
      {
         CF.setAttribute("anger",v0);
      }
      
      public function get angerAddMul() : Number
      {
         return CF.getAttribute("angerAddMul");
      }
      
      public function set angerAddMul(v0:Number) : void
      {
         CF.setAttribute("angerAddMul",v0);
      }
      
      public function get hurtMul() : Number
      {
         return CF.getAttribute("hurtMul");
      }
      
      public function set hurtMul(v0:Number) : void
      {
         CF.setAttribute("hurtMul",v0);
      }
      
      public function get proAddLv() : Number
      {
         return CF.getAttribute("proAddLv");
      }
      
      public function set proAddLv(v0:Number) : void
      {
         CF.setAttribute("proAddLv",v0);
      }
      
      public function get upgradeNum() : Number
      {
         return CF.getAttribute("upgradeNum");
      }
      
      public function set upgradeNum(v0:Number) : void
      {
         CF.setAttribute("upgradeNum",v0);
      }
      
      override public function inData_byXML(xml0:XML, father0:String) : void
      {
         father = father0;
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         var index0:int = ArrayMethod.getElementLimit(imgIndexArr,this.lv - 1) as int;
         if(iconLabel == "")
         {
            iconLabel = "weapon/" + this.baseLabel + "_" + index0 + "_icon";
         }
         if(name == "")
         {
            name = this.baseLabel + "_" + this.lv;
         }
         if(this.actionLabel == "")
         {
            this.actionLabel = this.weaponType + "Attack";
         }
         if(this.weaponUrl == "")
         {
            this.weaponUrl = "weapon/" + this.baseLabel + "_" + index0;
         }
         this.skin = this.weaponUrl.replace("weapon/","");
         type = EquipType.WEAPON;
         this.fleshDescription();
      }
      
      private function fleshDescription() : void
      {
         description = TextWay.fleshDescription(description);
      }
      
      public function getSkinName() : String
      {
         return this.skin;
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      override public function getAddObj() : Object
      {
         var obj0:Object = {};
         var lv0:int = this.lv + this.proAddLv;
         var add0:Number = (ArrayMethod.getElementLimit(dpsAddArr,lv0 - 1) as Number) / 100;
         obj0["dpsMul"] = add0;
         return obj0;
      }
      
      public function clone() : WeaponDefine
      {
         var d0:WeaponDefine = new WeaponDefine();
         d0.inData_byObj(this);
         return d0;
      }
      
      public function cloneBase() : WeaponDefine
      {
         var d0:WeaponDefine = this.clone();
         d0.iconLabel = "";
         d0.name = "";
         d0.actionLabel = "";
         d0.weaponUrl = "";
         return d0;
      }
      
      override public function getTrueCnName() : String
      {
         return cnName + this.lv + "级";
      }
      
      public function getArmsTypeCn() : String
      {
         return Gaming.defineGroup.armsCharger.getDefine(this.armsType).cnName;
      }
      
      public function getUpgradeMustName() : String
      {
         return this.baseLabel + "_1";
      }
      
      public function getUpgradeName() : String
      {
         return this.baseLabel + "_" + (this.lv + 1);
      }
      
      override public function getSortIndex() : int
      {
         var index0:int = index;
         var d0:WeaponDefine = Gaming.defineGroup.weapon.getDefine(this.getUpgradeMustName());
         if(Boolean(d0))
         {
            index0 = d0.index;
         }
         return index0 * 1000 + 99 - this.lv;
      }
      
      override public function getGoodsTip() : String
      {
         return this.getGatherTip();
      }
      
      public function getGatherTip(addObj0:Object = null, nowHurt0:Number = 0, surplusDay0:int = -1) : String
      {
         var str0:String = "";
         if(!addObj0)
         {
            addObj0 = this.getAddObj();
         }
         str0 += "<i1>|<blue <b>提升人物：</b>/>";
         str0 += "\n" + EquipPropertyDataCreator.getText_byObj(addObj0);
         str0 += "\n<i1>|<blue <b>属性：</b>/>";
         str0 += this.getProGatherStr(nowHurt0);
         if(surplusDay0 > 0)
         {
            str0 += "\n<gray 剩余寿命/>" + "|<purple " + surplusDay0 + "天/>";
         }
         else if(life > 0)
         {
            str0 += "\n<gray 使用寿命/>" + "|<purple " + life + "天/>";
         }
         var skillArr0:Array = this.getShowSkillArr();
         if(skillArr0.length > 0)
         {
            str0 += "\n\n<i1>|<blue <b>技能：</b>/>";
            str0 += "\n" + EquipSkillCreator.getAllSkillTip(skillArr0);
         }
         if(description != null && description != "")
         {
            str0 += "\n" + description;
         }
         return str0;
      }
      
      public function getShowSkillArr() : Array
      {
         var newArr0:Array = null;
         var name0:String = null;
         var d0:SkillDefine = null;
         if(skillArr.length > 0)
         {
            newArr0 = [];
            for each(name0 in skillArr)
            {
               d0 = Gaming.defineGroup.skill.getDefine(name0);
               if(d0.wantDescripB)
               {
                  newArr0.push(d0.name);
               }
            }
            return newArr0;
         }
         return skillArr;
      }
      
      public function getProGatherStr(nowHurt0:Number = 0) : String
      {
         var str0:String = "";
         str0 += "\n<gray 所需怒气/>|<yellow " + this.anger + "点/>";
         str0 += "\n<gray 相关武器/>|<yellow " + this.getArmsTypeCn() + "/>";
         str0 += "\n<gray 伤害倍数/>|<yellow " + this.hurtMul + "倍/>";
         if(nowHurt0 > 0)
         {
            str0 += "\n<gray 当前伤害/>|<orange " + ComMethod.numberToSmall(nowHurt0) + "/>";
         }
         return str0;
      }
      
      override public function canResolveB() : Boolean
      {
         if(this.lv >= 2)
         {
            return true;
         }
         return super.canResolveB();
      }
      
      override public function getResolveGift() : GiftAddDefineGroup
      {
         var all0:GiftAddDefineGroup = null;
         var i:int = 0;
         var must0:MustDefine = null;
         var gift0:GiftAddDefineGroup = null;
         if(this.lv >= 2)
         {
            all0 = new GiftAddDefineGroup();
            for(i = 2; i <= this.lv; i++)
            {
               must0 = WeaponDataCreator.getUpgradeThingsMust(this,i,"bag");
               gift0 = new GiftAddDefineGroup();
               gift0.inMustDefineOnlyThings(must0);
               all0.merge(gift0);
            }
            return all0;
         }
         return super.getResolveGift();
      }
      
      override public function dealBtnListCn(label0:String) : String
      {
         if(this.lv >= 2 && label0 == "resolve")
         {
            return "分解";
         }
         return "";
      }
   }
}

