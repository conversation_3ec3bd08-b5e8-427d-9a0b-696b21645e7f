package dataAll._app.achieve.define
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class AchieveDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var gather:String = "";
      
      public var father:String = "";
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var unlockLv:int = 0;
      
      public var achieveDiff:Number = 0;
      
      public var medelProArr:Array = [];
      
      public var condition:AchieveConditionDefine = new AchieveConditionDefine();
      
      public var fixedName:String = "";
      
      public var description:String = "";
      
      private var gift:GiftAddDefineGroup = null;
      
      public var iconUrl:String = "";
      
      public function AchieveDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML, father0:String, gather0:String) : void
      {
         ClassProperty.inData_byXML(this,xml0,pro_arr);
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         this.father = father0;
         this.gather = gather0;
         this.condition.inData_byXML(xml0.condition[0]);
         if(xml0.gift.length() > 0)
         {
            this.gift = new GiftAddDefineGroup();
            this.gift.inData_byXML(xml0.gift);
         }
         if(this.iconUrl == "")
         {
            this.iconUrl = "AchieveIcon/" + this.name;
         }
         this.fleshDescription();
         this.fleshCnName();
      }
      
      public function getGift() : GiftAddDefineGroup
      {
         return this.gift;
      }
      
      public function haveMedelProB() : Boolean
      {
         return this.medelProArr.length > 0;
      }
      
      private function fleshCnName() : void
      {
         this.cnName = this.fleshOneString(this.cnName);
      }
      
      private function fleshDescription() : void
      {
         this.description = this.fleshOneString(this.description,"#00FFFF");
         if(this.description.indexOf("[levelStr]") >= 0)
         {
            this.description = TextWay.replaceStr(this.description,"[levelStr]",this.condition.getLevelString());
         }
      }
      
      private function fleshOneString(str2:String, color0:String = "") : String
      {
         var str0:String = null;
         var yStr0:String = null;
         var rStr0:String = null;
         var cPro_arr0:Array = AchieveConditionDefine.pro_arr;
         for each(str0 in cPro_arr0)
         {
            yStr0 = "[condition." + str0 + "]";
            rStr0 = str0 == "mul" ? TextWay.numberToPer(this.condition[str0]) : this.condition[str0];
            if(color0 != "")
            {
               rStr0 = ComMethod.color(rStr0,color0);
            }
            str2 = TextWay.replaceStr(str2,yStr0,rStr0);
         }
         return str2;
      }
      
      public function sameProPan(proArr0:Array) : Boolean
      {
         var pro0:String = null;
         for each(pro0 in this.medelProArr)
         {
            if(proArr0.indexOf(pro0) >= 0)
            {
               return true;
            }
         }
         return false;
      }
   }
}

