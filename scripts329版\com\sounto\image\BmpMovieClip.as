package com.sounto.image
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.geom.Matrix;
   import flash.geom.Rectangle;
   
   public class BmpMovieClip extends SimulateMovieClip
   {
      
      private var BMP_ARR1:Array = null;
      
      private var RECT_ARR1:Array = null;
      
      private var BMP_ARR2:Array = null;
      
      private var RECT_ARR2:Array = null;
      
      public var bmpArr:Array = null;
      
      public var rectArr:Array = null;
      
      private var _scaleX:Number = 1;
      
      private var ra:Number = 0;
      
      private var ra_index:int = 0;
      
      private var raNum:int = 0;
      
      private var simpleEffectB:Boolean = false;
      
      public var nowBmp:BitmapData;
      
      public var nowRect:Rectangle;
      
      public var bitmap:Bitmap = new Bitmap();
      
      private var smoothing:Boolean = false;
      
      public function BmpMovieClip()
      {
         super();
         type = SimulateType.bmp;
         addChild(this.bitmap);
      }
      
      public function Switch(sp0:Sprite, rotateNum:int = 1, simpleEffectB0:Boolean = false) : *
      {
         var i:int = 0;
         var scale_num:int = 0;
         var s:int = 0;
         var rect0:Rectangle = null;
         var drawTarget:* = undefined;
         var mix:Matrix = null;
         var bmp0:BitmapData = null;
         var ra0:Number = NaN;
         var mc:MovieClip = sp0 as MovieClip;
         this.RECT_ARR1 = [];
         this.RECT_ARR2 = [];
         this.BMP_ARR1 = [];
         this.BMP_ARR2 = [];
         this.simpleEffectB = simpleEffectB0;
         _totalFrames = Boolean(mc) ? mc.totalFrames : 1;
         _currentFrame = 1;
         this.raNum = rotateNum;
         var sp_con:Sprite = new Sprite();
         var sp_con2:Sprite = new Sprite();
         var bmp_con:Bitmap = new Bitmap(null,"auto",true);
         sp_con.addChild(sp_con2);
         sp_con2.addChild(bmp_con);
         var allbmpnum0:int = 0;
         for(var n:int = 0; n <= _totalFrames - 1; n++)
         {
            if(Boolean(mc))
            {
               mc.gotoAndStop(n + 1);
            }
            this.BMP_ARR1[n] = [];
            this.BMP_ARR2[n] = [];
            this.RECT_ARR1[n] = [];
            this.RECT_ARR2[n] = [];
            for(i = 0; i < rotateNum; i++)
            {
               scale_num = this.simpleEffectB ? 1 : 2;
               for(s = 0; s < scale_num; s++)
               {
                  drawTarget = sp0;
                  if(i > 0 || s != 0)
                  {
                     ra0 = i * 360 / rotateNum;
                     sp_con2.rotation = ra0;
                     sp_con2.scaleX = s == 0 ? 1 : -1;
                     drawTarget = sp_con;
                     rect0 = sp_con.getRect(sp_con);
                  }
                  else
                  {
                     rect0 = sp0.getRect(sp0);
                  }
                  if(rect0.width == 0)
                  {
                     rect0.width = 1;
                  }
                  if(rect0.height == 0)
                  {
                     rect0.height = 1;
                  }
                  rect0.x = this.getInt(rect0.x);
                  rect0.y = this.getInt(rect0.y);
                  rect0.width = this.getInt(rect0.width);
                  rect0.height = this.getInt(rect0.height);
                  mix = new Matrix();
                  mix.tx = -rect0.x;
                  mix.ty = -rect0.y;
                  bmp0 = new BitmapData(rect0.width,rect0.height,true,0);
                  bmp0.draw(drawTarget,mix);
                  allbmpnum0++;
                  if(s == 0)
                  {
                     this.BMP_ARR1[n][i] = bmp0;
                     this.RECT_ARR1[n][i] = rect0;
                  }
                  else
                  {
                     this.BMP_ARR2[n][i] = bmp0;
                     this.RECT_ARR2[n][i] = rect0;
                  }
                  if(s == 0 && i == 0)
                  {
                     bmp_con.bitmapData = bmp0;
                     bmp_con.smoothing = true;
                     bmp_con.x = rect0.x;
                     bmp_con.y = rect0.y;
                  }
               }
            }
         }
         this.scaleX = 1;
         this.changeEvent();
      }
      
      private function getInt(num:Number) : int
      {
         var num0:int = int(num);
         var _num:int = num0;
         if(num0 > 0)
         {
            if(num0 < num)
            {
               _num = num0 + 1;
            }
         }
         else if(num0 > num)
         {
            _num = num0 - 1;
         }
         return _num;
      }
      
      public function copy() : BmpMovieClip
      {
         var newBmp:BmpMovieClip = new BmpMovieClip();
         newBmp.BMP_ARR1 = this.BMP_ARR1;
         newBmp.BMP_ARR2 = this.BMP_ARR2;
         newBmp.RECT_ARR1 = this.RECT_ARR1;
         newBmp.RECT_ARR2 = this.RECT_ARR2;
         newBmp.scaleX = 1;
         newBmp._currentFrame = 1;
         newBmp._totalFrames = _totalFrames;
         newBmp.raNum = this.raNum;
         newBmp.father = father;
         newBmp.label = label;
         newBmp.simpleEffectB = this.simpleEffectB;
         newBmp.smoothing = this.smoothing;
         newBmp.changeEvent();
         return newBmp;
      }
      
      public function inBmpEffectData(da0:BmpEffectData) : void
      {
         this.BMP_ARR1 = da0.bmpArr;
         this.BMP_ARR2 = da0.bmpArr;
         this.RECT_ARR1 = da0.rectArr;
         this.RECT_ARR2 = da0.rectArr;
         this.scaleX = 1;
         _currentFrame = 1;
         _totalFrames = da0.totalFrames;
         this.raNum = da0.getRaNum();
         father = da0.father;
         label = da0.label;
         this.changeEvent();
      }
      
      override public function get rotation() : Number
      {
         if(this.simpleEffectB)
         {
            return super.rotation;
         }
         return this.ra;
      }
      
      override public function set rotation(value:Number) : void
      {
         if(this.simpleEffectB)
         {
            super.rotation = value;
            return;
         }
         this.ra = value % 360;
         if(this.ra < 0)
         {
            this.ra += 360;
         }
         if(this.raNum > 1)
         {
            this.ra_index = int((this.ra + 180 / this.raNum) / 360 * this.raNum) % this.raNum;
         }
         else
         {
            this.ra_index = 0;
         }
      }
      
      public function setTrueRotation(value:Number) : void
      {
         super.rotation = value;
      }
      
      override public function get scaleX() : Number
      {
         return this._scaleX;
      }
      
      override public function set scaleX(value:Number) : void
      {
         if(this.simpleEffectB)
         {
            super.scaleX = value;
            this._scaleX = value;
            this.bmpArr = this.BMP_ARR1;
            this.rectArr = this.RECT_ARR1;
            return;
         }
         if(value >= 0)
         {
            this._scaleX = 1;
            this.bmpArr = this.BMP_ARR1;
            this.rectArr = this.RECT_ARR1;
         }
         else
         {
            this._scaleX = -1;
            this.bmpArr = this.BMP_ARR2;
            this.rectArr = this.RECT_ARR2;
         }
      }
      
      override public function setMustScaleX(v0:Number) : void
      {
         this.scaleX = v0;
         if(this.simpleEffectB == false && v0 != 1 && v0 != -1)
         {
            super.scaleX = Math.abs(v0);
         }
      }
      
      override public function setMustScaleY(v0:Number) : void
      {
         this.scaleY = v0;
         if(this.simpleEffectB == false && v0 != 1 && v0 != -1)
         {
            super.scaleY = Math.abs(v0);
         }
      }
      
      override public function get scaleY() : Number
      {
         return 1;
      }
      
      override public function set scaleY(value:Number) : void
      {
      }
      
      public function setSmoothing(bb0:Boolean) : void
      {
         if(Boolean(this.bitmap))
         {
            this.bitmap.smoothing = bb0;
         }
         this.smoothing = bb0;
      }
      
      override public function gotoAndPlay(num:int) : *
      {
         super.gotoAndPlay(num);
         this.changeEvent();
      }
      
      override public function gotoAndStop(num:int) : *
      {
         super.gotoAndStop(num);
         this.changeEvent();
      }
      
      override public function changeEvent() : void
      {
         this.nowBmp = this.bmpArr[_currentFrame - 1][this.ra_index];
         this.nowRect = this.rectArr[_currentFrame - 1][this.ra_index];
         this.bitmap.bitmapData = this.nowBmp;
         if(this.smoothing)
         {
            this.bitmap.smoothing = this.smoothing;
         }
         this.bitmap.x = this.nowRect.x;
         this.bitmap.y = this.nowRect.y;
      }
      
      override public function FTimer() : void
      {
         if(isPlaying && enabled)
         {
            if(now_s % speed == 0)
            {
               now_s = 0;
               if(this.raNum > 0)
               {
                  this.changeEvent();
                  if(_currentFrame >= _totalFrames)
                  {
                     _currentFrame = 1;
                  }
                  else
                  {
                     ++_currentFrame;
                  }
               }
            }
            ++now_s;
         }
      }
   }
}

