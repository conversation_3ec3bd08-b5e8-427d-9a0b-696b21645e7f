package dataAll.body.extra
{
   import com.sounto.utils.ClassProperty;
   import dataAll.body.define.NormalBodyDefine;
   
   public class BodyExtraDefineGroup
   {
      
      public static var pro_arr:Array = [];
      
      private static var eyeLightArr:Array = ["","super","trueBoss"];
      
      public var arr:Array = [];
      
      public var obj:Object = {};
      
      public var label:String = "";
      
      public var bossSkillArr:Array = [];
      
      public var stateArr:Array = [];
      
      public var allExclusiveSkillArr:Array = [];
      
      public function BodyExtraDefineGroup()
      {
         super();
      }
      
      private static function getEyeLight(index0:int) : String
      {
         if(index0 < 0)
         {
            index0 = 0;
         }
         if(index0 > eyeLightArr.length - 1)
         {
            index0 = eyeLightArr.length - 1;
         }
         return eyeLightArr[index0];
      }
      
      public function inData_byXML(xml0:XML, bodyD0:NormalBodyDefine) : void
      {
         var n:int = 0;
         var x0:XML = null;
         var d0:BodyExtraDefine = null;
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         var xl0:XMLList = xml0.extra;
         if(xl0 is XMLList)
         {
            n = 0;
            for each(x0 in xl0)
            {
               d0 = new BodyExtraDefine();
               d0.inData_byXML(x0);
               if(d0.name == "")
               {
                  d0.name = "e" + d0.lifeMin;
               }
               if(d0.eyeLight == "")
               {
                  d0.eyeLight = getEyeLight(n);
               }
               this.arr.push(d0);
               this.obj[d0.name] = d0;
               if(d0.lifeMin == 1)
               {
                  INIT.showError(bodyD0.cnName + "，" + d0.name + " 没有lifeMin属性");
               }
               n++;
            }
         }
         this.allExclusiveSkillArr = this.getAllExclusiveSkillArr();
      }
      
      public function getExtraByLifePer(per0:Number) : BodyExtraDefine
      {
         var d0:BodyExtraDefine = null;
         for each(d0 in this.arr)
         {
            if(per0 >= d0.lifeMin)
            {
               return d0;
            }
         }
         return null;
      }
      
      private function getAllExclusiveSkillArr() : Array
      {
         var d0:BodyExtraDefine = null;
         var arr0:Array = [];
         for each(d0 in this.arr)
         {
            arr0 = arr0.concat(d0.skillArr);
         }
         return arr0;
      }
   }
}

