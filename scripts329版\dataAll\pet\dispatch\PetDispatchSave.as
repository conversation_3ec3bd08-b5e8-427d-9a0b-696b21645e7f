package dataAll.pet.dispatch
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   
   public class PetDispatchSave
   {
      
      public static var pro_arr:Array = null;
      
      public static const NO:String = "no";
      
      public static const ING:String = "ing";
      
      public static const OVER:String = "over";
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var idArr:Array = [];
      
      public var todayB:Boolean = false;
      
      public function PetDispatchSave()
      {
         super();
         this.startTime = 0;
      }
      
      public static function getDispatchMax() : int
      {
         return 3;
      }
      
      public function get startTime() : Number
      {
         return this.CF.getAttribute("startTime");
      }
      
      public function set startTime(v0:Number) : void
      {
         this.CF.setAttribute("startTime",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.todayB = false;
      }
      
      public function setNewIdArr(arr0:Array) : void
      {
         this.idArr = arr0;
      }
      
      public function addId(id0:String) : void
      {
         if(this.idArr.length < getDispatchMax())
         {
            ArrayMethod.addNoRepeatInArr(this.idArr,id0);
         }
      }
      
      public function removeId(id0:String) : void
      {
         ArrayMethod.remove(this.idArr,id0);
      }
      
      public function isDispatchB() : Boolean
      {
         return this.startTime > 0;
      }
   }
}

