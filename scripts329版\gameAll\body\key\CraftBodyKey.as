package gameAll.body.key
{
   import com.sounto.key.Keys;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.image.CarImage;
   import gameAll.bullet.BulletLauncher;
   
   public class CraftBodyKey extends NormalBodyKey
   {
      
      private var carImg:CarImage;
      
      private var bulletLauncher:BulletLauncher;
      
      public function CraftBodyKey(_BB:IO_NormalBody)
      {
         super(_BB);
         this.carImg = _BB.getImg() as CarImage;
         this.bulletLauncher = _BB.getBullet();
      }
      
      override public function setShootPoint(x0:int, y0:int, tweenMul0:Number = 0) : void
      {
         super.setShootPoint(x0,y0);
         this.bulletLauncher.setShootPoint(x0,y0,tweenMul0);
      }
      
      protected function shootDown() : void
      {
         this.bulletLauncher.mouseDown();
      }
      
      protected function shootUp() : void
      {
         this.bulletLauncher.mouseUp();
      }
      
      override public function outMouseDown(e:* = null) : void
      {
         dat.getCtrlBody().getKey().mouseArmsAction(true);
      }
      
      override public function outMouseUp(e:* = null) : void
      {
         dat.getCtrlBody().getKey().mouseArmsAction(false);
      }
      
      override protected function doArmsAction(downB0:Boolean) : void
      {
         if(downB0)
         {
            this.shootDown();
         }
         else
         {
            this.shootUp();
         }
      }
      
      override public function keyPan(arr0:Array) : void
      {
         if(ai.enabled)
         {
            return;
         }
         moveOrFly(arr0);
         comboPan(arr0);
         var kJ:Keys = arr0[keySave.getCode("shoot")];
         if(kJ is Keys)
         {
            if(kJ.s == "down")
            {
               this.shootDown();
            }
            if(kJ.s == "up")
            {
               this.shootUp();
            }
         }
         skillPan(arr0);
         this.armsPan(arr0);
      }
      
      private function armsPan(arr0:Array) : void
      {
         var code0:int = 0;
         var kQ:Keys = null;
         var nextArmsKeyArr0:Array = keySave.getCodeArr("nextArms");
         for each(code0 in nextArmsKeyArr0)
         {
            kQ = arr0[code0];
            if(kQ.s == "down")
            {
               this.bulletLauncher.nextArms();
               break;
            }
         }
      }
   }
}

