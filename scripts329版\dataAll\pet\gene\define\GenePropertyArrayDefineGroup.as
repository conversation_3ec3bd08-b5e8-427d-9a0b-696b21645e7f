package dataAll.pet.gene.define
{
   import dataAll.pro.PropertyArrayDefine;
   import dataAll.pro.PropertyArrayDefineGroup;
   
   public class GenePropertyArrayDefineGroup extends PropertyArrayDefineGroup
   {
      
      public function GenePropertyArrayDefineGroup()
      {
         super();
      }
      
      override protected function getNewDefine() : PropertyArrayDefine
      {
         return new GenePropertyArrayDefine();
      }
   }
}

