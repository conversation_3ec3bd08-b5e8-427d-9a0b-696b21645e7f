package dataAll.items.creator
{
   import dataAll._app.parts.define.PartsType;
   import dataAll.arms.ArmsData;
   import dataAll.arms.define.ArmsChargerDefine;
   import dataAll.arms.save.ArmsSave;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.save.EquipSave;
   import dataAll.must.define.MustDefine;
   
   public class ItemsUpgradeCtrl
   {
      
      public function ItemsUpgradeCtrl()
      {
         super();
      }
      
      public static function getArmsMust(da0:ArmsData, lvAdd0:int) : MustDefine
      {
         var s0:ArmsSave = da0.save;
         var d0:MustDefine = new MustDefine();
         d0.lv = s0.getTrueLevel() + lvAdd0;
         d0.coin = Gaming.defineGroup.normal.getLevelCoinIncome(d0.lv);
         var num0:int = getConverStone(s0.getTrueLevel(),s0.color);
         if(da0.isArenaGiftB())
         {
            if(da0.save.addLevel < 5 && s0.getTrueLevel() < 90)
            {
               num0 = 1;
            }
         }
         var chargerD0:ArmsChargerDefine = Gaming.defineGroup.armsCharger.getDefine(da0.armsType);
         var partsArr0:Array = getPartsMustThingsArr(chargerD0.mustPartArr.length,s0.color,s0.getTrueLevel());
         d0.inThingsDataByArr(["converStone;" + num0].concat(partsArr0));
         return d0;
      }
      
      public static function getEquipMust(da0:EquipData, lvAdd0:int) : MustDefine
      {
         var s0:EquipSave = da0.save;
         var d0:MustDefine = new MustDefine();
         d0.lv = s0.getTrueLevel() + lvAdd0;
         d0.coin = Gaming.defineGroup.normal.getLevelCoinIncome(d0.lv) * 3;
         var num0:int = getConverStone(s0.getTrueLevel(),s0.color);
         num0 = int(num0 * 1.5);
         if(da0.isArenaGiftB())
         {
            if(da0.save.addLevel < 5)
            {
               num0 = 1;
            }
            else
            {
               num0 /= 2;
            }
         }
         var partsArr0:Array = getPartsMustThingsArr(EquipType.getUpgradePartNum(s0.partType),s0.color,s0.getTrueLevel());
         d0.inThingsDataByArr(["converStone;" + num0].concat(partsArr0));
         return d0;
      }
      
      private static function getPartsMustThingsArr(partNumMul0:Number, color0:String, lv0:int) : Array
      {
         var partsLv0:int = 0;
         var partsNum0:int = 0;
         var arr0:Array = null;
         var str0:String = null;
         var colorIndex0:int = EquipColor.getIndex(color0);
         if(colorIndex0 >= 6 && lv0 >= 91)
         {
            partsLv0 = 81;
            partsNum0 = 1;
            if(lv0 <= 93)
            {
               partsLv0 = 81;
               partsNum0 = 3;
            }
            else if(lv0 <= 94)
            {
               partsLv0 = 84;
               partsNum0 = 1;
            }
            else if(lv0 <= 100)
            {
               partsLv0 = 84;
               partsNum0 = 2;
            }
            if(color0 == EquipColor.DARKGOLD)
            {
               partsNum0 *= 2;
            }
            partsNum0 *= partNumMul0;
            arr0 = [];
            str0 = PartsType.NORMAL_PARTS + "_" + partsLv0 + ";" + partsNum0;
            arr0.push(str0);
            return arr0;
         }
         return [];
      }
      
      public static function getAllConverStone(baseLv0:int, nowAddLevel0:int, color0:String, equipB0:Boolean = false) : int
      {
         var num2:int = 0;
         var num0:int = 0;
         for(var i:int = 0; i < nowAddLevel0; i++)
         {
            num2 = getConverStone(baseLv0 + i,color0) * (equipB0 ? 1.5 : 1);
            num0 += num2;
         }
         return num0;
      }
      
      private static function getConverStone(lv0:int, color0:String) : int
      {
         var c0:Number = EquipColor.getMustColorMul(color0);
         var v0:int = Gaming.defineGroup.dataList.getValue("itemsUpgradeStone",lv0);
         return Math.ceil(v0 * c0);
      }
      
      public static function test() : void
      {
         for(var i:int = 1; i < 95; i++)
         {
            trace(getConverStone(i,EquipColor.RED));
         }
      }
   }
}

