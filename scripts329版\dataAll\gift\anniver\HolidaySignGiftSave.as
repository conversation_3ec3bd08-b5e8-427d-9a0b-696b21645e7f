package dataAll.gift.anniver
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.ObjectMethod;
   import dataAll._data.ConstantDefine;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class HolidaySignGiftSave
   {
      
      public static var pro_arr:Array = [];
      
      public static var GIFT_NAME:String = "anniverSign";
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var getObj:Object = {};
      
      public var dailyB:Boolean = false;
      
      public var eggObj:Object = {};
      
      public var eggTO:Object = {};
      
      public function HolidaySignGiftSave()
      {
         super();
         this.num = 0;
      }
      
      public function get num() : Number
      {
         return this.CF.getAttribute("num");
      }
      
      public function set num(v0:Number) : void
      {
         this.CF.setAttribute("num",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.getObj = ClassProperty.copyObj(obj0["getObj"]);
         this.eggObj = ClassProperty.copyObj(obj0["eggObj"]);
         this.eggTO = ClassProperty.copyObj(obj0["eggTO"]);
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.setDayGetB(false);
         this.dailyB = false;
         this.eggObj = {};
      }
      
      public function getLastGetB() : Boolean
      {
         var gArr0:Array = this.getDefineGiftArr();
         var g0:GiftAddDefineGroup = gArr0[gArr0.length - 1];
         return this.getGiftGetB(String(g0.mustLevel));
      }
      
      public function getGiftGetB(name0:String) : Boolean
      {
         return this.getObj[name0];
      }
      
      public function setGiftGetB(name0:String, bb0:Boolean) : void
      {
         this.getObj[name0] = bb0;
      }
      
      public function getDayGetB() : Boolean
      {
         return this.getGiftGetB("1");
      }
      
      public function setDayGetB(bb0:Boolean) : void
      {
         return this.setGiftGetB("1",bb0);
      }
      
      public function daily() : void
      {
         if(!this.dailyB)
         {
            this.dailyB = true;
            ++this.num;
         }
      }
      
      public function getGift() : GiftAddDefineGroup
      {
         var g0:GiftAddDefineGroup = null;
         var gift0:GiftAddDefineGroup = new GiftAddDefineGroup();
         var gArr0:Array = this.getGiftArr();
         for each(g0 in gArr0)
         {
            gift0.merge(g0);
         }
         return gift0;
      }
      
      public function getFillNum(timeStr0:String) : int
      {
         var day0:int = 0;
         var v0:int = 0;
         var maxMust0:int = 0;
         var max0:int = 24;
         if(this.num >= max0)
         {
            return 0;
         }
         day0 = StringDate.compareDateByStr(ConstantDefine.anniver.signStart,timeStr0);
         if(!this.dailyB)
         {
            day0 -= 1;
         }
         v0 = day0 - this.num;
         maxMust0 = max0 - this.num;
         if(v0 > maxMust0)
         {
            v0 = maxMust0;
         }
         return v0;
      }
      
      public function fill(num0:int) : void
      {
         this.num += num0;
      }
      
      public function getGiftBtnTip() : String
      {
         var g0:GiftAddDefineGroup = null;
         var str0:String = "";
         var gArr0:Array = this.getGiftArr();
         if(gArr0.length == 0)
         {
            str0 = "礼包已领取";
         }
         else
         {
            for each(g0 in gArr0)
            {
               str0 += "\n";
               if(g0.cnName == "")
               {
                  str0 += "<blue " + g0.mustLevel + "天礼包/>";
               }
               else
               {
                  str0 += "<blue " + g0.cnName + "礼包/>";
               }
            }
            str0 = "包含：" + str0;
         }
         return str0;
      }
      
      private function getGiftArr() : Array
      {
         var g0:GiftAddDefineGroup = null;
         var must0:int = 0;
         var getB0:Boolean = false;
         var canB0:Boolean = false;
         var arr0:Array = [];
         var gArr0:Array = this.getDefineGiftArr();
         var anum0:int = this.num;
         for each(g0 in gArr0)
         {
            must0 = g0.mustLevel;
            getB0 = this.getGiftGetB(String(must0));
            canB0 = anum0 >= must0;
            if(must0 <= 1)
            {
               canB0 = this.dailyB;
            }
            if(!getB0 && canB0)
            {
               arr0.push(g0);
            }
         }
         return arr0;
      }
      
      public function getDefineGiftArr() : Array
      {
         return Gaming.defineGroup.gift.getArrByFather(GIFT_NAME);
      }
      
      public function getGiftEvent() : void
      {
         var g0:GiftAddDefineGroup = null;
         var must0:int = 0;
         var canB0:Boolean = false;
         var gArr0:Array = this.getDefineGiftArr();
         for each(g0 in gArr0)
         {
            must0 = g0.mustLevel;
            canB0 = this.num >= must0;
            if(canB0)
            {
               this.setGiftGetB(String(must0),true);
            }
         }
      }
      
      public function getGettedGift() : GiftAddDefineGroup
      {
         var g0:GiftAddDefineGroup = null;
         var name0:String = null;
         var getB0:Boolean = false;
         var gift0:GiftAddDefineGroup = new GiftAddDefineGroup();
         var gArr0:Array = this.getDefineGiftArr();
         for each(g0 in gArr0)
         {
            name0 = String(g0.mustLevel);
            getB0 = this.getGiftGetB(name0);
            if(getB0)
            {
               gift0.merge(g0);
            }
         }
         return gift0;
      }
      
      public function openEggGift() : GiftAddDefine
      {
         var n:* = undefined;
         var d0:GiftAddDefine = null;
         var name0:String = null;
         var max0:Number = NaN;
         var now0:Number = NaN;
         var maxObj0:Object = {
            "partsChest87":5,
            "demStone":20,
            "madheart":15,
            "arenaChest":30
         };
         var g0:GiftAddDefineGroup = this.getEggGiftAll().clone();
         for(n in maxObj0)
         {
            max0 = Number(maxObj0[n]);
            now0 = this.getEggThingsNum(n);
            if(now0 >= max0)
            {
               g0.removeGift(g0.getGiftByName(n));
            }
         }
         d0 = g0.getRandomDefine(null);
         name0 = d0.name;
         if(this.eggTO.hasOwnProperty(name0) == false)
         {
            this.eggTO[name0] = 0;
         }
         this.eggTO[name0] += d0.num;
         return d0;
      }
      
      private function getEggThingsNum(name0:String) : Number
      {
         return ObjectMethod.getEleIfHave(this.eggTO,name0,0) as Number;
      }
      
      public function getEggGiftAll() : GiftAddDefineGroup
      {
         return Gaming.defineGroup.gift.getOne("anniverEgg");
      }
      
      public function getEggSurplus(active0:int) : int
      {
         return this.getEggAll(active0) - this.getEggUse();
      }
      
      public function getEggAll(active0:int) : int
      {
         var v0:int = active0 / 10;
         if(v0 > 9)
         {
            v0 = 9;
         }
         return v0 + 1;
      }
      
      public function getEggUse() : int
      {
         var bb0:Boolean = false;
         var num0:int = 0;
         for each(bb0 in this.eggObj)
         {
            if(bb0)
            {
               num0++;
            }
         }
         return num0;
      }
      
      public function getEggB(index0:int) : Boolean
      {
         return this.eggObj[String(index0)];
      }
      
      public function setEggB(index0:int, bb0:Boolean) : void
      {
         this.eggObj[String(index0)] = bb0;
      }
   }
}

