package com.sounto.image
{
   import flash.display.DisplayObject;
   import flash.geom.Matrix;
   import flash.geom.Point;
   
   public class DisplayMotionDefine
   {
      
      public static const ZERO:DisplayMotionDefine = new DisplayMotionDefine();
      
      public var label:String = "";
      
      public var imgLabel:String = "";
      
      public var x:Number = 0;
      
      public var y:Number = 0;
      
      public var rotation:Number = 0;
      
      public var visible:Boolean = true;
      
      public var alpha:Number = 1;
      
      public var scaleX:Number = 1;
      
      public var scaleY:Number = 1;
      
      public var matrix:Matrix = null;
      
      public var deepIndex:int = 0;
      
      public function DisplayMotionDefine()
      {
         super();
      }
      
      public function toString() : String
      {
         return this.label;
      }
      
      public function setSP(sp0:DisplayObject) : void
      {
         sp0.x = this.x;
         sp0.y = this.y;
         sp0.rotation = this.rotation;
         sp0.scaleX = this.scaleX;
         sp0.scaleY = this.scaleY;
      }
      
      public function toPoint() : Point
      {
         return new Point(this.x,this.y);
      }
   }
}

