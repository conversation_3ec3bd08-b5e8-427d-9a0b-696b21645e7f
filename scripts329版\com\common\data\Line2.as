package com.common.data
{
   public class Line2
   {
      
      public var x:Number = 0;
      
      public var y:Number = 0;
      
      public var x1:Number = 0;
      
      public var y1:Number = 0;
      
      public var ra:Number = 0;
      
      public var w:Number = 0;
      
      public var len:Number = 900;
      
      public function Line2(_x0:Number = 0, _y0:Number = 0, _ra:Number = 0, _w:Number = 0, _len:Number = 900)
      {
         super();
         this.x = _x0;
         this.y = _y0;
         this.ra = _ra;
         this.w = _w;
         this.len = _len;
      }
      
      public function init() : void
      {
         this.x = 0;
         this.y = 0;
         this.x1 = 0;
         this.y1 = 0;
         this.ra = 0;
         this.w = 0;
         this.len = 0;
      }
      
      public function clone() : Line2
      {
         return new Line2(this.x,this.y,this.ra,this.w,this.len);
      }
      
      public function toString() : String
      {
         return "x:" + this.x + ",y:" + this.y + ",ra:" + this.ra + ",w:" + this.w;
      }
      
      public function countGap(l0:Object) : Number
      {
         return Math.sqrt((this.x - l0.x) * (this.x - l0.x) + (this.y - l0.y) * (this.y - l0.y));
      }
   }
}

