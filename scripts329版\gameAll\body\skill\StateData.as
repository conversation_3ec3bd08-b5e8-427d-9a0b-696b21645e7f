package gameAll.body.skill
{
   import dataAll.body.attack.HurtData;
   import dataAll.skill.SkillAddData;
   import dataAll.skill.define.SkillDefine;
   import gameAll.body.IO_NormalBody;
   import gameAll.bullet.BulletBody;
   import gameAll.effect.followEffect.IO_FollowEffectProducer;
   import gameAll.skill.InstantEffectCtrl;
   import gameAll.skill.SkillEffectData;
   import gameAll.skill.StateEffectCtrl;
   
   public class StateData implements IO_FollowEffectProducer
   {
      
      public var BB:IO_NormalBody = null;
      
      public var producer:IO_NormalBody = null;
      
      public var effectType:String = "";
      
      public var value:Number = 0;
      
      public var mul:Number = 1;
      
      public var extraValue:Number = 1;
      
      public var T:Number = 0;
      
      public var t:Number = 0;
      
      public var doGap:Number = 0;
      
      public var doGap_t:Number = 0;
      
      public var die:int = 0;
      
      public var positiveB:Boolean = true;
      
      public var overlyingNum:Number = 0;
      
      public var hurtData:HurtData = null;
      
      public var skillData:SkillData = null;
      
      public var define:SkillDefine = null;
      
      public var addData:SkillAddData = null;
      
      public var extra:StateExtraData = new StateExtraData();
      
      public function StateData()
      {
         super();
      }
      
      public function inData(d0:SkillDefine, addData0:SkillAddData = null) : void
      {
         this.define = d0;
         if(addData0 is SkillAddData)
         {
            this.addData = addData0;
         }
         else
         {
            this.addData = SkillAddData.ZERO;
         }
         this.effectType = d0.effectType;
         this.value = this.getValue();
         this.mul = this.getMul();
         this.T = this.getDuration();
         this.t = 0;
         this.doGap = d0.doGap;
         this.die = 0;
      }
      
      public function isEnemyB(meCamp0:String) : Boolean
      {
         if(this.producer is IO_NormalBody)
         {
            return this.producer.getData().camp != meCamp0;
         }
         return false;
      }
      
      public function isWeB(meCamp0:String) : Boolean
      {
         if(this.producer is IO_NormalBody)
         {
            return this.producer.getData().camp == meCamp0;
         }
         return false;
      }
      
      public function getDie() : int
      {
         return this.die;
      }
      
      public function getEffectVisible() : Boolean
      {
         return this.extra.visible;
      }
      
      public function getFromBullet() : BulletBody
      {
         if(Boolean(this.hurtData))
         {
            return this.hurtData.fromBullet;
         }
         return null;
      }
      
      public function FTimer() : void
      {
         var doB0:Boolean = false;
         var ps0:IO_LifeStateExtraObj = null;
         if(this.doGap == 0)
         {
            doB0 = StateEffectCtrl.doEffect(this.BB,this);
            if(doB0)
            {
               ps0 = this.extra.obj as IO_LifeStateExtraObj;
               if(Boolean(ps0))
               {
                  if(!ps0.isDieB())
                  {
                     this.t = 0;
                  }
               }
            }
         }
         else if(this.doGap_t >= this.doGap)
         {
            InstantEffectCtrl.doEffect_byStateData(this.BB,this);
         }
         if(this.t >= this.T)
         {
            this.die = 1;
         }
         else
         {
            this.t += 1 / 30;
         }
         if(this.doGap_t >= this.doGap)
         {
            this.doGap_t = 0;
         }
         else
         {
            this.doGap_t += 1 / 30;
         }
      }
      
      public function waitToDie() : void
      {
         this.t = this.T;
      }
      
      public function closeToDie() : Boolean
      {
         return this.t >= this.T - 2 / 30;
      }
      
      public function setEnemyStateTMul(mul0:Number) : void
      {
         this.T = this.getDuration() * mul0;
      }
      
      public function addProficiencySecond(timeGap0:Number = 1) : void
      {
         if(Boolean(this.skillData))
         {
            this.skillData.addProficiencySecond(timeGap0);
         }
      }
      
      public function getCd() : Number
      {
         return this.define.cd + this.addData.cd;
      }
      
      public function getValue() : Number
      {
         return this.define.value + this.addData.value;
      }
      
      public function getMul() : Number
      {
         return this.define.mul + this.addData.mul;
      }
      
      public function getValueString() : String
      {
         return this.define.valueString;
      }
      
      public function getDuration() : Number
      {
         return this.define.duration + this.addData.duration;
      }
      
      public function getRange() : Number
      {
         return this.define.range + this.addData.range;
      }
      
      public function getTempSkillEffectData() : SkillEffectData
      {
         var da0:SkillEffectData = new SkillEffectData();
         da0.init(this.define,this.BB,this.producer,this.hurtData);
         return da0;
      }
      
      public function getTempSkillEffectDataAndExtra() : SkillEffectData
      {
         var da0:SkillEffectData = this.getTempSkillEffectData();
         da0.extraValue = this.extraValue;
         return da0;
      }
   }
}

