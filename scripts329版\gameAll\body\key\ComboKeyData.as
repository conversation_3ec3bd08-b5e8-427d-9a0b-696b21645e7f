package gameAll.body.key
{
   import com.sounto.key.Keys;
   import com.sounto.key.KeysState;
   import dataAll._app.setting.key.SettingKeySave;
   
   public class Combo<PERSON>eyData extends Keys
   {
      
      public var gap:Number = 0;
      
      public function ComboKeyData(_code:int = 0)
      {
         super(_code);
      }
      
      public function inComboStr(str0:String, keySave0:SettingKeySave) : void
      {
         var label0:String = str0;
         var lastStr0:String = str0.substr(str0.length - 1,1);
         if(lastStr0 != "*" || lastStr0 != "^")
         {
            lastStr0 = "*";
         }
         else
         {
            label0 = str0.substr(0,str0.length - 1);
         }
         if(lastStr0 == "*")
         {
            s = KeysState.DOWN;
         }
         else if(lastStr0 == "*")
         {
            s = KeysState.UP;
         }
         label = label0;
         code = keySave0.getCode(label0);
      }
      
      public function toString() : String
      {
         return label + "";
      }
   }
}

