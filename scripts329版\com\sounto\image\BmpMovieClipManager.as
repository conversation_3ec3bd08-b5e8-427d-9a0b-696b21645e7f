package com.sounto.image
{
   import com.sounto.utils.ArrayMethod;
   import dataAll.body.define.NormalBodyDefine;
   import flash.display.BitmapData;
   import flash.display.Sprite;
   
   public class BmpMovieClipManager
   {
      
      public var obj:Object = new Object();
      
      private var keepFatherArr0:Array = [];
      
      public function BmpMovieClipManager()
      {
         super();
      }
      
      private function addKeepFather(father0:String) : void
      {
         ArrayMethod.addNoRepeatInArr(this.keepFatherArr0,father0);
      }
      
      public function addBodyResourceKeep(name0:String) : void
      {
         var label0:String = null;
         var sp0:Sprite = null;
         var d0:NormalBodyDefine = Gaming.defineGroup.body.getDefine(name0);
         this.addKeepFather(name0);
         for each(label0 in d0.imgArr)
         {
            sp0 = Gaming.swfLoaderManager.getResource(name0,label0);
            this.addResource(sp0,name0,label0);
         }
      }
      
      public function addResource(sp0:Sprite, father0:String, label0:String, raNum:int = 1, simpleEffectB:Boolean = false) : BmpMovieClip
      {
         var bmp0:BmpMovieClip = this.getBmp(father0,label0);
         if(bmp0 is BmpMovieClip)
         {
            return bmp0;
         }
         bmp0 = new BmpMovieClip();
         bmp0.Switch(sp0,raNum,simpleEffectB);
         bmp0.father = father0;
         bmp0.label = label0;
         if(!this.obj.hasOwnProperty(father0))
         {
            this.obj[father0] = new Object();
         }
         this.obj[father0][label0] = bmp0;
         return bmp0;
      }
      
      public function haveBodyResource(father0:String) : Boolean
      {
         return this.obj.hasOwnProperty(father0);
      }
      
      public function getBmp(father0:String, label0:String) : BmpMovieClip
      {
         var obj2:Object = null;
         if(this.obj.hasOwnProperty(father0))
         {
            obj2 = this.obj[father0];
            if(obj2.hasOwnProperty(label0))
            {
               return obj2[label0];
            }
         }
         return null;
      }
      
      public function getBmpCopy(father0:String, label0:String) : BmpMovieClip
      {
         var obj2:Object = null;
         if(this.obj.hasOwnProperty(father0))
         {
            obj2 = this.obj[father0];
            if(obj2.hasOwnProperty(label0))
            {
               return obj2[label0].copy();
            }
         }
         return null;
      }
      
      public function clearResource(noFatherArr0:Array) : void
      {
         var n:* = undefined;
         var obj2:Object = null;
         var i:* = undefined;
         var bmp0:BmpMovieClip = null;
         var new_obj0:Object = {};
         for(n in this.obj)
         {
            obj2 = this.obj[n];
            if(noFatherArr0.indexOf(n) == -1 && this.keepFatherArr0.indexOf(n) == -1)
            {
               for(i in obj2)
               {
                  bmp0 = obj2[i];
                  this.clearBmpMovieClip(bmp0);
               }
            }
            else
            {
               new_obj0[n] = obj2;
            }
         }
         this.obj = new_obj0;
      }
      
      private function clearBmpMovieClip(bmp0:BmpMovieClip) : void
      {
         var arr2:Array = null;
         var arr_len2:int = 0;
         var i:int = 0;
         var bmp1:BitmapData = null;
         var arr0:Array = bmp0.bmpArr;
         var arr_len0:int = int(arr0.length);
         for(var n:int = 0; n < arr_len0; n++)
         {
            arr2 = arr0[n];
            arr_len2 = int(arr2.length);
            for(i = 0; i < arr_len2; i++)
            {
               bmp1 = arr2[i];
               bmp1.dispose();
            }
         }
      }
   }
}

