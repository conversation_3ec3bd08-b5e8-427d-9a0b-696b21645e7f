package dataAll.level.define.unit
{
   public class AiType
   {
      
      public static const NO:String = "no";
      
      public static const FOLLOW_POINT:String = "followPoint";
      
      public static const FOLLOW_POINT_ATTACK:String = "followPointAttack";
      
      public static const FOLLOW_BODY:String = "followBody";
      
      public static const FOLLOW_BODY_ATTACK:String = "followBodyAttack";
      
      public static const ATTACK_BODY:String = "attackBody";
      
      public static const PATROL:String = "patrol";
      
      public static const PATROL_BIG:String = "patrol";
      
      public static const PATROL_GLOBAL:String = "patrolGlobal";
      
      public static const followPointAttack_WarningRange:int = 600;
      
      public static const followBodyAttack_WarningRange:int = 1000;
      
      public static const attackBody_WarningRange:int = 1000;
      
      public static const patrol_WarningRange:int = 600;
      
      public static const patrolBig_WarningRange:int = 700;
      
      public static const patrolGlobal_WarningRange:int = 999999;
      
      public function AiType()
      {
         super();
      }
      
      public static function getWarningRange(name0:String) : int
      {
         if(Boolean(AiType[name0 + "_WarningRange"]))
         {
            return AiType[name0 + "_WarningRange"];
         }
         return followBodyAttack_WarningRange;
      }
   }
}

