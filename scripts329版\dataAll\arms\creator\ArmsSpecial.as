package dataAll.arms.creator
{
   public class ArmsSpecial
   {
      
      public static const twoShootPro:String = "twoShootPro";
      
      public static const penetrationNum:String = "penetrationNum";
      
      public static const crit:String = "crit";
      
      public static const floorBounce:String = "floorBounce";
      
      public static const bodyBounce:String = "bodyBounce";
      
      public static const penetrationGap:String = "penetrationGap";
      
      public static const arr:Array = [twoShootPro,penetrationNum,crit,floorBounce,bodyBounce,penetrationGap];
      
      public static const specialArr:Array = [twoShootPro,penetrationNum,crit,floorBounce,bodyBounce];
      
      public static const cnArr:Array = ["瞬间连发","穿人个数","暴击","地面反弹","击中反弹","穿墙距离"];
      
      public function ArmsSpecial()
      {
         super();
      }
      
      public static function getCn(name0:String) : String
      {
         return cnArr[arr.indexOf(name0)];
      }
   }
}

