package com.sounto.test
{
   import UI.base.HaveConSprite;
   import flash.display.Bitmap;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   import flash.system.System;
   import flash.text.StyleSheet;
   import flash.text.TextField;
   import flash.utils.getTimer;
   
   public class Stats extends HaveConSprite
   {
      
      protected const WIDTH:int = 70;
      
      protected const HEIGHT:int = 30;
      
      protected var xml:XML;
      
      protected var text:TextField;
      
      protected var style:StyleSheet;
      
      protected var timer:int;
      
      protected var fps:int;
      
      protected var ms:int;
      
      protected var ms_prev:int;
      
      protected var mem:Number;
      
      protected var mem_max:Number;
      
      protected var graph:Bitmap;
      
      protected var rectangle:Rectangle;
      
      protected var fps_graph:int;
      
      protected var mem_graph:int;
      
      protected var mem_max_graph:int;
      
      protected var theme:Object = {
         "bg":51,
         "fps":16776960,
         "ms":65280,
         "mem":65535,
         "memmax":16711792
      };
      
      public function Stats(_theme:Object = null)
      {
         super();
         if(Bo<PERSON>an(_theme))
         {
            if(_theme.bg != null)
            {
               this.theme.bg = _theme.bg;
            }
            if(_theme.fps != null)
            {
               this.theme.fps = _theme.fps;
            }
            if(_theme.ms != null)
            {
               this.theme.ms = _theme.ms;
            }
            if(_theme.mem != null)
            {
               this.theme.mem = _theme.mem;
            }
            if(_theme.memmax != null)
            {
               this.theme.memmax = _theme.memmax;
            }
         }
         this.mem_max = 0;
         this.xml = <xml><fps>FPS:</fps><mem>MEM:</mem><memMax>MAX:</memMax></xml>;
         this.style = new StyleSheet();
         this.style.setStyle("xml",{
            "fontSize":"9px",
            "fontFamily":"_sans",
            "leading":"-2px"
         });
         this.style.setStyle("fps",{"color":this.hex2css(this.theme.fps)});
         this.style.setStyle("ms",{"color":this.hex2css(this.theme.ms)});
         this.style.setStyle("mem",{"color":this.hex2css(this.theme.mem)});
         this.style.setStyle("memMax",{"color":this.hex2css(this.theme.memmax)});
         this.text = new TextField();
         this.text.width = this.WIDTH;
         this.text.height = 50;
         this.text.styleSheet = this.style;
         this.text.condenseWhite = true;
         this.text.selectable = false;
         this.text.mouseEnabled = false;
         this.graph = new Bitmap();
         this.graph.y = 50;
         this.rectangle = new Rectangle(this.WIDTH - 1,0,1,this.HEIGHT - 50);
         addEventListener(Event.ADDED_TO_STAGE,this.init,false,0,true);
         addEventListener(Event.REMOVED_FROM_STAGE,this.destroy,false,0,true);
      }
      
      public function show() : void
      {
         inCon();
         visible = true;
      }
      
      public function hide() : void
      {
         outCon();
         visible = false;
      }
      
      private function init(e:Event) : void
      {
         graphics.beginFill(this.theme.bg);
         graphics.drawRect(0,0,this.WIDTH,this.HEIGHT);
         graphics.endFill();
         addChild(this.text);
         this.text.y = -3;
         addEventListener(Event.ENTER_FRAME,this.update);
      }
      
      private function destroy(e:Event) : void
      {
         graphics.clear();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         if(Boolean(this.graph.bitmapData))
         {
            this.graph.bitmapData.dispose();
         }
         removeEventListener(MouseEvent.CLICK,this.onClick);
         removeEventListener(Event.ENTER_FRAME,this.update);
      }
      
      private function update(e:Event) : void
      {
         if(!visible)
         {
            return;
         }
         this.timer = getTimer();
         if(this.timer - 1000 > this.ms_prev)
         {
            this.ms_prev = this.timer;
            this.mem = Number((System.totalMemory * 9.54e-7).toFixed(3));
            this.mem_max = this.mem_max > this.mem ? this.mem_max : this.mem;
            this.fps_graph = Math.min(this.graph.height,this.fps / stage.frameRate * this.graph.height);
            this.mem_graph = Math.min(this.graph.height,Math.sqrt(Math.sqrt(this.mem * 5000))) - 2;
            this.mem_max_graph = Math.min(this.graph.height,Math.sqrt(Math.sqrt(this.mem_max * 5000))) - 2;
            this.xml.fps = "FPS: " + this.fps + " / " + stage.frameRate;
            this.xml.mem = "MEM: " + this.mem;
            this.xml.memMax = "MAX: " + this.mem_max;
            this.fps = 0;
         }
         ++this.fps;
         this.ms = this.timer;
         this.text.htmlText = this.xml;
      }
      
      private function onClick(e:MouseEvent) : void
      {
         if(mouseY / height > 0.5)
         {
            --stage.frameRate;
         }
         else
         {
            ++stage.frameRate;
         }
         this.xml.fps = "FPS: " + this.fps + " / " + stage.frameRate;
         this.text.htmlText = this.xml;
      }
      
      private function hex2css(color:int) : String
      {
         return "#" + color.toString(16);
      }
   }
}

