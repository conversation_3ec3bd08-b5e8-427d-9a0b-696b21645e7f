package dataAll.skill.save
{
   import com.sounto.utils.ClassProperty;
   import dataAll.items.save.ItemsSaveGroup;
   
   public class HeroSkillSaveGroup extends ItemsSaveGroup
   {
      
      public static var pro_arr:Array = [];
      
      public var delNameArr:Array = [];
      
      public function HeroSkillSaveGroup()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         super.inData_byObjAndClass(obj0,HeroSkillSave);
         if(!(obj0["delNameArr"] is Array))
         {
            obj0["delNameArr"] = [];
         }
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
   }
}

