package dataAll.skill.define
{
   import com.sounto.utils.ClassProperty;
   
   public class SkillTargetDefine
   {
      
      public static var pro_arr:Array = [];
      
      public static const MOUSE:String = "mouse";
      
      public static const ME:String = "me";
      
      public static const TARGET:String = "target";
      
      public static const ALERT_TARGET:String = "alertTarget";
      
      public static const OTHER_ZOMBIE_WOLF:String = "otherZombieWolf";
      
      public static const ME_SUMMONED_FATHER:String = "meSummonedFather";
      
      public static const ATTACK_TARGET:String = "attackTarget";
      
      public static const RANDOM:String = "random";
      
      public static const RANGE:String = "range";
      
      public static const NEAR:String = "near";
      
      public var text:String = "me";
      
      public var target:String = "";
      
      public var chooseType:String = "";
      
      public var camp:String = "";
      
      public var unitType:String = "";
      
      public var systemType:String = "";
      
      public var limitNum:int = 0;
      
      public var noExistB:Boolean = false;
      
      public var arenaB:Boolean = true;
      
      public var bodyName:String = "";
      
      public var noRaceType:String = "";
      
      public var targetMustLiveB:Boolean = false;
      
      public var noVehicleB:Boolean = false;
      
      public var noMeB:Boolean = false;
      
      public var noMainB:Boolean = false;
      
      public var alert:String = "";
      
      public function SkillTargetDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         this.inData(String(xml0));
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      private function inData(str0:String) : void
      {
         if(str0 == "" || !str0)
         {
            return;
         }
         this.text = str0;
         var strArr0:Array = str0.split(",");
         this.target = strArr0[0];
         if(strArr0.length > 1)
         {
            this.chooseType = strArr0[1];
            this.camp = strArr0[2];
            if(strArr0.length >= 4)
            {
               this.unitType = strArr0[3];
               if(strArr0.length >= 5)
               {
                  this.systemType = strArr0[4];
                  if(strArr0.length >= 6)
                  {
                     this.limitNum = int(String(strArr0[5]));
                  }
               }
            }
         }
      }
      
      public function clone() : SkillTargetDefine
      {
         var d0:SkillTargetDefine = new SkillTargetDefine();
         d0.inData_byObj(this);
         return d0;
      }
   }
}

