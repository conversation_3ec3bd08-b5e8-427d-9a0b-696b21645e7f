package dataAll.pro
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll.ui.GatherColor;
   
   public class ProType
   {
      
      public static const BOOLEAN:String = "boolean";
      
      public static const INT:String = "int";
      
      public static const UINT:String = "uint";
      
      public static const NUMBER:String = "number";
      
      public static const STRING:String = "string";
      
      public static const ARRAY:String = "array";
      
      public static const OBJECT:String = "object";
      
      public static const NULL:String = "null";
      
      public static const skill:String = "skill";
      
      public function ProType()
      {
         super();
      }
      
      public static function getType(v0:*) : String
      {
         if(v0 == null)
         {
            return NULL;
         }
         if(v0 is Boolean)
         {
            return BOOLEAN;
         }
         if(v0 is Number)
         {
            return NUMBER;
         }
         if(v0 is String)
         {
            return STRING;
         }
         if(v0 is Array)
         {
            return ARRAY;
         }
         return OBJECT;
      }
      
      public static function isNum(type0:String) : Boolean
      {
         return type0 == NUMBER || type0 == INT || type0 == UINT;
      }
      
      public static function booleanToUI(bb0:Boolean) : String
      {
         if(bb0)
         {
            return ComMethod.color("√",GatherColor.greenColor);
         }
         return ComMethod.color("×",GatherColor.gray2Color);
      }
      
      public static function booleanToUIRed(bb0:Boolean) : String
      {
         if(bb0)
         {
            return ComMethod.color("√",GatherColor.greenColor);
         }
         return ComMethod.color("×",GatherColor.redColor);
      }
      
      public static function booleanToCn(bb0:Boolean) : String
      {
         if(bb0)
         {
            return "是";
         }
         return "否";
      }
      
      public static function stringToType(s0:String, type0:String) : *
      {
         if(type0 == BOOLEAN)
         {
            return Boolean(int(s0));
         }
         if(type0 == INT || type0 == UINT)
         {
            return int(s0);
         }
         if(type0 == NUMBER)
         {
            return Number(s0);
         }
         if(type0 == ARRAY)
         {
            return s0.split(",");
         }
         return s0;
      }
      
      public static function getInit(type0:String) : *
      {
         if(type0 == BOOLEAN)
         {
            return false;
         }
         if(type0 == NUMBER || type0 == INT || type0 == UINT)
         {
            return 0;
         }
         if(type0 == STRING)
         {
            return "";
         }
         if(type0 == ARRAY)
         {
            return [];
         }
         return null;
      }
   }
}

