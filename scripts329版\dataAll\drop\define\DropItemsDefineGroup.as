package dataAll.drop.define
{
   import dataAll._app.food.FoodRawDefine;
   import dataAll.equip.device.DeviceDefine;
   import dataAll.equip.weapon.WeaponDefine;
   import dataAll.things.define.ThingsDefine;
   import dataAll.things.define.ThingsType;
   
   public class DropItemsDefineGroup
   {
      
      public var obj:Object = {};
      
      public function DropItemsDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var father_xml0:XML = null;
         var type0:String = null;
         var drop_xmlList0:XMLList = null;
         var i:* = undefined;
         var drop_xm0:XML = null;
         var d0:DropItemsDefine = null;
         var father_xmlList0:XMLList = xml0.father;
         for(n in father_xmlList0)
         {
            father_xml0 = father_xmlList0[n];
            type0 = father_xml0.@type;
            drop_xmlList0 = father_xml0.drop;
            for(i in drop_xmlList0)
            {
               drop_xm0 = drop_xmlList0[i];
               d0 = new DropItemsDefine();
               d0.inData_byXML(drop_xm0,type0);
               this.addDefine(d0);
            }
         }
      }
      
      private function addDefine(d0:DropItemsDefine) : void
      {
         if(!this.obj.hasOwnProperty(d0.type))
         {
            this.obj[d0.type] = {};
         }
         this.obj[d0.type][d0.name] = d0;
      }
      
      public function getDefine(type0:String, name0:String) : DropItemsDefine
      {
         return this.obj[type0][name0];
      }
      
      public function getMustImgUrlList() : Array
      {
         var n:* = undefined;
         var i:* = undefined;
         var d0:DropItemsDefine = null;
         var arr0:Array = [];
         for(n in this.obj)
         {
            for(i in this.obj[n])
            {
               d0 = this.obj[n][i];
               arr0.push(d0.imgUrl);
            }
         }
         return arr0;
      }
      
      public function getRandomLabel(type0:String) : String
      {
         var n:* = undefined;
         var d0:DropItemsDefine = null;
         var obj0:Object = this.obj[type0];
         var list0:Array = [];
         for(n in obj0)
         {
            d0 = obj0[n];
            list0.push(d0.name);
         }
         return list0[int(list0.length * Math.random())];
      }
      
      public function inFoodRawDefine(d0:FoodRawDefine) : void
      {
         var c0:DropItemsDefine = new DropItemsDefine();
         c0.type = DropItemsDefine.TYPE_FOOD;
         c0.name = d0.name;
         c0.cnName = d0.cnName;
         c0.text = "获得食材";
         c0.imgUrl = d0.iconUrl;
         this.addDefine(c0);
      }
      
      public function inDeviceDefine(d0:DeviceDefine) : void
      {
         var itemsDefine0:DropItemsDefine = new DropItemsDefine();
         itemsDefine0.type = "equip";
         itemsDefine0.name = d0.name + "_1";
         itemsDefine0.cnName = d0.cnName;
         itemsDefine0.text = "获得装置";
         itemsDefine0.imgUrl = d0.iconLabel;
         this.addDefine(itemsDefine0);
      }
      
      public function inWeaponDefine(d0:WeaponDefine) : void
      {
         var itemsDefine0:DropItemsDefine = new DropItemsDefine();
         itemsDefine0.type = "equip";
         itemsDefine0.name = d0.name;
         itemsDefine0.cnName = d0.cnName;
         itemsDefine0.text = "获得副手";
         itemsDefine0.imgUrl = d0.iconLabel;
         this.addDefine(itemsDefine0);
      }
      
      public function addThingsAll(obj0:Object) : void
      {
         var td0:ThingsDefine = null;
         var d0:DropItemsDefine = null;
         for each(td0 in obj0)
         {
            if(td0.addDropDefineB)
            {
               d0 = new DropItemsDefine();
               d0.type = "things";
               d0.name = td0.name;
               d0.cnName = td0.cnName;
               d0.text = "获得物品";
               d0.imgUrl = td0.iconUrl;
               d0.secType = td0.father;
               if(td0.isPartsSpecialB())
               {
                  d0.lightImg = "lightEffect/redDrop";
               }
               else if(ThingsType.purgoldArr.indexOf(td0.name) >= 0)
               {
                  d0.lightImg = "lightEffect/purgoldDrop";
               }
               this.addDefine(d0);
            }
         }
      }
   }
}

