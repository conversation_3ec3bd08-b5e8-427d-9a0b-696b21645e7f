package gameAll.body.motion
{
   import com.sounto.math.Maths;
   
   public class AircraftGroundMotion extends NormalGroundMotion
   {
      
      public static var MAX_RA:Number = 25 / 180 * Math.PI;
      
      public function AircraftGroundMotion()
      {
         super();
      }
      
      override public function motionTimer() : void
      {
         if(enabled)
         {
            super.motionTimer();
         }
      }
      
      public function setShootPoint(mouse_x:int, mouse_y:int) : void
      {
         var _MAX:Number = MAX_RA;
         var ra0:Number = Math.atan2(mouse_y - y,mouse_x - x);
         if(mouse_x < x)
         {
            ra0 = Maths.ZhunJ(Math.PI - ra0);
         }
         if(ra0 > _MAX)
         {
            ra0 = _MAX;
         }
         else if(ra0 < -_MAX)
         {
            ra0 = -_MAX;
         }
         var mRa0:Number = mouse_x < x ? -ra0 : ra0;
         inputSlopeRa(mRa0,false);
      }
   }
}

