package dataAll.equip.creator
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldNiuBiCF;
   import dataAll.equip.EquipData;
   import dataAll.equip.save.EquipSave;
   import dataAll.items.creator.ItemsUpgradeCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.pro.PropertyArrayDefine;
   
   public class EquipUpgradeCtrl
   {
      
      private static var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public function EquipUpgradeCtrl()
      {
         super();
      }
      
      public static function get maxAddLevel() : Number
      {
         return CF.getAttribute("maxAddLevel");
      }
      
      public static function set maxAddLevel(v0:Number) : void
      {
         CF.setAttribute("maxAddLevel",v0);
      }
      
      public static function init() : void
      {
         maxAddLevel = 19;
      }
      
      private static function color(str0:String) : String
      {
         return ComMethod.color(str0,"#FFFF00");
      }
      
      public static function canUpgradeB(da0:EquipData) : Boolean
      {
         return da0.save.addLevel < maxAddLevel;
      }
      
      public static function getStateStr(da0:EquipData, cData0:EquipData, beforeB0:Boolean = true) : String
      {
         var s0:EquipSave = da0.save;
         var lv0:int = s0.getTrueLevel();
         var max0:int = s0.getMaxLevel();
         var str0:String = "";
         if(lv0 > max0)
         {
            str0 += ComMethod.color("<b>已升级至最高等级</b>","#00FF00");
         }
         else
         {
            str0 += "<i1>|<green <b>" + (beforeB0 ? "当前" : "升级后") + lv0 + "级</b>/>" + (beforeB0 ? "<gray2 (最高可升至" + max0 + "级)/>" : "");
            str0 += "\n" + EquipPropertyDataCreator.getText_byObj(da0.save.getTrueObj(),cData0.save.getTrueObj(),true);
         }
         return str0;
      }
      
      public static function getAfterData(da0:EquipData) : EquipData
      {
         var affter_s0:EquipSave = da0.save.clone();
         ++affter_s0.addLevel;
         var obj0:Object = da0.save.obj;
         affter_s0.obj = getUpgradeObj(obj0,1,affter_s0.getTrueLevel());
         var affter_da0:EquipData = affter_s0.getDataClass();
         affter_da0.inData_bySave(affter_s0,null);
         return affter_da0;
      }
      
      private static function getUpgradeObj(obj0:Object, addLv0:int, nowLv0:int) : Object
      {
         var n:* = undefined;
         var v0:Number = NaN;
         var v2:Number = NaN;
         var proD0:PropertyArrayDefine = null;
         var obj2:Object = {};
         for(n in obj0)
         {
            v0 = Number(obj0[n]);
            v2 = v0;
            proD0 = EquipPropertyDataCreator.getPropertyArrayDefine(n);
            if(proD0 is PropertyArrayDefine)
            {
               if(Boolean(proD0.dataArr))
               {
                  if(proD0.dataArr.length > 10)
                  {
                     v2 = countUpgradePro(obj0[n],n,addLv0,nowLv0);
                  }
               }
            }
            obj2[n] = v2;
         }
         return obj2;
      }
      
      private static function countUpgradePro(v0:Number, proName0:String, addLv0:int, nowLv0:int) : Number
      {
         var v2:Number = 0;
         var lv1:int = EquipPropertyDataCreator.getLvInOnePro(v0,proName0);
         var min1:Number = EquipPropertyDataCreator.getValueByPro(proName0,lv1 - 1);
         var max1:Number = EquipPropertyDataCreator.getValueByPro(proName0,lv1);
         var min2:Number = EquipPropertyDataCreator.getValueByPro(proName0,lv1 - 1 + addLv0);
         var max2:Number = EquipPropertyDataCreator.getValueByPro(proName0,lv1 + addLv0);
         var _max:Number = EquipPropertyDataCreator.getValueByPro(proName0,nowLv0 + addLv0);
         var per1:Number = 0.5;
         if(min1 < max1)
         {
            per1 = (v0 - min1) / (max1 - min1);
         }
         v2 = min2 + (max2 - min2) * per1;
         if(v2 > _max)
         {
            v2 = _max;
         }
         var d0:PropertyArrayDefine = EquipPropertyDataCreator.getPropertyArrayDefine(proName0);
         return d0.fixedNumber(v2);
      }
      
      public static function upgradeOne(da0:EquipData) : void
      {
         var s0:EquipSave = da0.save;
         var after_da0:EquipData = getAfterData(da0);
         s0.obj = after_da0.save.obj;
         s0.addLevel += 1;
      }
      
      public static function getMust(da0:EquipData) : MustDefine
      {
         return ItemsUpgradeCtrl.getEquipMust(da0,1);
      }
   }
}

