package UI.helper.book
{
   import UI.UIOrder;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import UI.base.grid.NormalGridIcon;
   import UI.base.scroll.NormalScrollBar;
   import UI.base.text.MixedTextField;
   import com.common.net.SaveImage;
   import com.sounto.utils.TextMethod;
   import dataAll._app.book.IO_BookDefine;
   import dataAll._player.base.PlayerMainData;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.ui.text.MixedTextLabel;
   import flash.display.Graphics;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.geom.Rectangle;
   import flash.text.TextField;
   
   public class HelperBookBox extends AutoNormalUI
   {
      
      private var closeBtn:NormalBtn;
      
      private var getterBtn:NormalBtn;
      
      private var icon:NormalGridIcon = new NormalGridIcon();
      
      private var mixed:MixedTextField = new MixedTextField();
      
      private var titleTxt:TextField;
      
      private var infoTxt:TextField;
      
      private var cardSp:Sprite;
      
      private var chipSp:Sprite;
      
      private var shadowSp:Sprite;
      
      private var maskTargetSp:Sprite;
      
      private var scrollBarSp:Sprite;
      
      private var scrollLineSp:Sprite;
      
      private var scrollBar:NormalScrollBar;
      
      private var scrollCon:Sprite = new Sprite();
      
      private var nowDefine:IO_BookDefine = null;
      
      private var cardProListTip:String = "";
      
      public function HelperBookBox()
      {
         super();
         mcTypeArr = ["btnSp","txt"];
      }
      
      private static function get mainData() : PlayerMainData
      {
         return Gaming.PG.da.main;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = [];
         super.setImg(img0);
         addChild(this.scrollCon);
         this.infoTxt.parent.removeChild(this.infoTxt);
         this.mixed.inTextFieldData(this.infoTxt);
         this.scrollCon.addChild(this.mixed);
         this.scrollCon.addChild(this.titleTxt);
         this.cardSp["txt"].styleSheet = TextMethod.getLinkCss();
         this.cardSp["txt"].addEventListener(TextEvent.LINK,this.cardTextLink);
         this.cardSp["mouseSp"].addEventListener(MouseEvent.MOUSE_OVER,this.cardOver);
         this.cardSp["mouseSp"].addEventListener(MouseEvent.MOUSE_OUT,this.cardOut);
         this.chipSp["txt"].styleSheet = TextMethod.getLinkCss();
         this.chipSp["txt"].addEventListener(TextEvent.LINK,this.chipTextLink);
         addChild(this.icon);
         this.scrollBar = new NormalScrollBar(this.scrollCon,this.maskTargetSp,this.scrollBarSp,this.scrollLineSp,1,false,true,true);
         this.scrollBar.speed = 30;
         this.scrollBar.refresh();
         if(Gaming.isLocal())
         {
            this.icon.addEventListener(MouseEvent.CLICK,this.iconClick);
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
      }
      
      public function iconClick(e:MouseEvent = null) : void
      {
         this.icon.saveToPng(this.nowDefine.getCnName());
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var id0:String = null;
         var gift0:GiftAddDefine = null;
         var g0:GiftAddDefineGroup = null;
         var bb0:Boolean = false;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0 == this.closeBtn)
         {
            hide();
         }
         else if(btn0 == this.getterBtn)
         {
            id0 = this.nowDefine.getBookId();
            gift0 = this.nowDefine.getBookGift();
            if(mainData.canBookGetB(id0))
            {
               // 移除重复拥有检查，允许重复获取图鉴武器
               // 这样图鉴获取功能就和刷指定武器功能保持一致
               g0 = new GiftAddDefineGroup();
               g0.addGift(gift0);
               bb0 = GiftAddit.addAndAutoBagSpacePan(g0,"领取成功！");
               if(bb0)
               {
                  mainData.getBookIdEvent(id0);
                  this.fleshBtn();
               }
            }
         }
      }
      
      public function showDefine(d0:IO_BookDefine) : void
      {
         this.nowDefine = d0;
         this.fleshData();
         this.show();
      }
      
      private function fleshData() : void
      {
         var d0:IO_BookDefine = this.nowDefine;
         this.titleTxt.text = d0.getCnName();
         this.fleshChip(d0);
         this.fleshCard(d0);
         this.icon.setIconName(d0.getBookImgUrl());
         this.fleshPosition();
         this.mixed.setMixedText(d0.getBookInfo() + (FontDeal.countGap > 0 ? "\n\n\n\n\n\n\n" : ""));
         this.fleshSrollSize();
         this.scrollBar.refresh();
         this.fleshBtn();
      }
      
      private function fleshSrollSize() : void
      {
         var gh0:Graphics = this.scrollCon.graphics;
         gh0.clear();
         gh0.beginFill(0,0);
         gh0.drawRect(this.titleTxt.x,this.titleTxt.y - 20,5,this.mixed.y + this.mixed.height + 20 * 2);
      }
      
      private function fleshPosition() : void
      {
         var rect0:Rectangle = this.icon.getRect(this.icon);
         this.icon.x = this.shadowSp.x - (rect0.x + rect0.width / 2);
         this.icon.y = this.shadowSp.y - (rect0.y + rect0.height);
         this.chipSp.y = this.icon.y * 2;
         this.cardSp.y = this.icon.y * 2;
         if(this.chipSp.visible)
         {
            this.cardSp.y += this.chipSp.height;
         }
      }
      
      private function fleshBtn() : void
      {
         var getNum0:int = 0;
         var canB0:Boolean = Boolean(this.nowDefine.getBookCanGet());
         if(canB0)
         {
            this.getterBtn.visible = true;
            if(mainData.canBookGetB(this.nowDefine.getBookId()))
            {
               getNum0 = mainData.getBookGetNum(this.nowDefine.getBookId());
               this.getterBtn.actived = true;
               this.getterBtn.setName(getNum0 >= 0 ? "再次领取" : "领取");
            }
            else
            {
               this.getterBtn.actived = false;
               this.getterBtn.setName("已领取");
            }
         }
         else
         {
            this.getterBtn.visible = false;
         }
      }
      
      private function fleshCard(d0:IO_BookDefine) : void
      {
         this.setCardText("");
      }
      
      private function cardOver(e:MouseEvent) : void
      {
         if(this.cardProListTip != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(this.cardProListTip);
         }
      }
      
      private function cardOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function setCardText(text0:String) : void
      {
         if(text0 == "")
         {
            this.cardSp.visible = false;
         }
         else
         {
            this.cardSp.visible = true;
            this.cardSp["txt"].htmlText = MixedTextLabel.mixedToHtmlText(text0);
         }
      }
      
      private function cardTextLink(e:TextEvent) : void
      {
         if(e.text == "compose")
         {
         }
      }
      
      private function fleshChip(d0:IO_BookDefine) : void
      {
         this.setChipText("");
      }
      
      private function setChipText(text0:String) : void
      {
         if(text0 == "")
         {
            this.chipSp.visible = false;
         }
         else
         {
            this.chipSp.visible = true;
            this.chipSp["txt"].htmlText = MixedTextLabel.mixedToHtmlText(text0);
         }
      }
      
      private function chipTextLink(e:TextEvent) : void
      {
         if(e.text == "compose")
         {
         }
      }
      
      private function chipCompose() : void
      {
      }
      
      public function saveToPng() : void
      {
         if(visible)
         {
            SaveImage.SaveByMC(this,new Rectangle(33,33,713,463),this.nowDefine.getCnName() + "属性");
         }
      }
   }
}

