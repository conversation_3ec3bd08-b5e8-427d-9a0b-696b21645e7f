package dataAll.arms.bookGet
{
   public class ArmsBookGetDefine
   {
      
      public var name:String = "";
      
      public var mustArr2:Array = [];
      
      public var title:String = "";
      
      public function ArmsBookGetDefine()
      {
         super();
      }
      
      public function inMustStr(mustStr0:String) : void
      {
         var s0:String = null;
         var arr2:Array = null;
         var marr2:Array = null;
         var one0:ArmsBookGetOne = null;
         this.mustArr2.length = 0;
         var arr1:Array = mustStr0.split(",");
         for each(s0 in arr1)
         {
            arr2 = s0.split("|");
            marr2 = [];
            for each(s0 in arr2)
            {
               one0 = new ArmsBookGetOne();
               one0.inData(s0);
               marr2.push(one0);
            }
            this.mustArr2.push(marr2);
         }
      }
   }
}

