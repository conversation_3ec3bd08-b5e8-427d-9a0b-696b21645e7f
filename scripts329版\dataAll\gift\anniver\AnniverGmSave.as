package dataAll.gift.anniver
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class AnniverGmSave
   {
      
      public static var pro_arr:Array = [];
      
      public static var GIFT_NAME:String = "anniverGm";
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public function AnniverGmSave()
      {
         super();
         this.useNum = 0;
         this.buyNum = 0;
      }
      
      public function get useNum() : Number
      {
         return this.CF.getAttribute("useNum");
      }
      
      public function set useNum(v0:Number) : void
      {
         this.CF.setAttribute("useNum",v0);
      }
      
      public function get buyNum() : Number
      {
         return this.CF.getAttribute("buyNum");
      }
      
      public function set buyNum(v0:Number) : void
      {
         this.CF.setAttribute("buyNum",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.buyNum = 0;
         this.useNum = 0;
      }
      
      public function getGift(timeDa0:StringDate) : GiftAddDefineGroup
      {
         var day0:int = timeDa0.getDateClass().day;
         var giftName0:String = day0 == 0 || day0 % 2 == 1 ? GIFT_NAME + "1" : GIFT_NAME + "2";
         return Gaming.defineGroup.gift.getOne(giftName0);
      }
      
      public function getGiftArr() : Array
      {
         return Gaming.defineGroup.gift.getArrByFather(GIFT_NAME);
      }
      
      public function useOne() : void
      {
         ++this.useNum;
      }
      
      public function getMaxNum() : int
      {
         return 1 + this.buyNum;
      }
      
      public function getSurplusNum() : int
      {
         return this.getMaxNum() - this.useNum;
      }
      
      public function getBuyMax() : int
      {
         return 4;
      }
      
      public function getCanBuyNum() : int
      {
         return this.getBuyMax() - this.buyNum;
      }
      
      public function buyOne(num0:int) : void
      {
         this.buyNum += num0;
      }
   }
}

