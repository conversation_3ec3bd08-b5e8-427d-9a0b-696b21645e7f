package dataAll.bullet
{
   import com.sounto.utils.ClassProperty;
   
   public class BulletFollowDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var value:Number = 0;
      
      public var delay:Number = 0;
      
      public var maxTime:Number = 10000;
      
      public var hitIsTargetB:Boolean = false;
      
      public var noLM:Boolean = false;
      
      public function BulletFollowDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function copy() : BulletFollowDefine
      {
         var d0:BulletFollowDefine = new BulletFollowDefine();
         ClassProperty.inData(d0,this,pro_arr);
         return d0;
      }
   }
}

