package dataAll.skill.define
{
   import com.adobe.serialization.json.JSON2;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.edit._def.EditIDDefine;
   import dataAll._base.IO_Define;
   import dataAll._base.IO_TipDefine;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.bullet.BulletDefine;
   import dataAll.image.ImageUrlDefine;
   import dataAll.skill.define.otherObj.SkillBulletObj;
   import dataAll.skill.define.otherObj.SkillSummoneObj;
   import dataAll.ui.GatherColor;
   
   public class SkillDefine implements IO_TipDefine, IO_Define
   {
      
      public static const ACTIVE:String = "active";
      
      public static const PASSIVE:String = "passive";
      
      public static const STATE:String = "state";
      
      public static const INSTANT:String = "instant";
      
      public static var UI_SPECIAL:Boolean = false;
      
      private static var perProArr:Array = ["secMul","mul","obj.pro","obj.per"];
      
      public static var pro_arr:Array = [];
      
      public static var imgNameArr:Array = ["addSkillEffectImg","meEffectImg","targetEffectImg","pointEffectImg","otherEffectImg","stateEffectImg","stateEffectImg2"];
      
      public var father:String = "";
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var iconUrl36:String = "";
      
      public var baseLabel:String = "";
      
      public var everNoClearB:Boolean = false;
      
      public var noEffectLevelModel:String = "";
      
      public var showInLifeBarB:Boolean = false;
      
      public var noInClonedB:Boolean = false;
      
      public var summonedUnitsB:Boolean = false;
      
      public var noBeClearB:Boolean = false;
      
      public var changeHurtB:Boolean = false;
      
      public var noSkillDodgeB:Boolean = false;
      
      public var ignoreImmunityB:Boolean = false;
      
      public var ignoreNoSkillB:Boolean = false;
      
      public var ignoreSilenceB:Boolean = false;
      
      public var noCopyB:Boolean = false;
      
      public var noRandomListB:Boolean = false;
      
      public var createByArmsTypePro:String = "";
      
      public var haveEffectBuletHitNum:int = 9999;
      
      public var isAttackB:Boolean = false;
      
      public var isDefenceB:Boolean = false;
      
      public var isInvincibleB:Boolean = false;
      
      public var isCtrlB:Boolean = false;
      
      public var mustLv:int = 0;
      
      public var keyName:String = "";
      
      public var conditionType:String = "passive";
      
      public var condition:String = "";
      
      public var doCondition:String = "";
      
      public var otherConditionArr:Array = [];
      
      public var conditionRange:Number = 0;
      
      public var conditionString:String = "";
      
      public var applyArr:Array = [];
      
      public var target:SkillTargetDefine = new SkillTargetDefine();
      
      public var addType:String = "state";
      
      public var overlyingB:Boolean = false;
      
      public var noReStateB:Boolean = false;
      
      public var effectFather:String = "";
      
      public var effectType:String = "";
      
      public var stateRemoveEvent:String = "";
      
      public var effectProArr:Array = [];
      
      public var value:Number = 0;
      
      public var mul:Number = 1;
      
      public var secMul:Number = 1;
      
      public var valueString:String = "";
      
      public var secString:String = "";
      
      public var extraValueType:String = "";
      
      public var intervalT:Number = 0;
      
      public var minTriggerT:Number = 0;
      
      public var firstTriggerT:Number = 0;
      
      public var cd:Number = 0;
      
      public var firstCd:Number = -1;
      
      public var continueNum:int = 1;
      
      public var noCdMulB:Boolean = false;
      
      public var cdRandomRange:Number = 0;
      
      public var delay:Number = 0;
      
      public var groundDieB:Boolean = false;
      
      public var duration:Number = 0;
      
      public var doGap:Number = 0;
      
      public var range:Number = 0;
      
      public var minRange:Number = 0;
      
      public var obj:Object = {};
      
      public var passiveSkillArr:Array = [];
      
      public var linkArr:Array = [];
      
      public var meActionLabel:String = "";
      
      public var aiSelectorB:Boolean = false;
      
      public var targetPointEffectB:Boolean = false;
      
      public var ie:String = "";
      
      public var addSkillEffectImg:ImageUrlDefine = new ImageUrlDefine();
      
      public var meEffectImg:ImageUrlDefine = new ImageUrlDefine();
      
      public var targetEffectImg:ImageUrlDefine = new ImageUrlDefine();
      
      public var pointEffectImg:ImageUrlDefine = new ImageUrlDefine();
      
      public var otherEffectImg:ImageUrlDefine = new ImageUrlDefine();
      
      public var stateEffectImg:ImageUrlDefine = new ImageUrlDefine();
      
      public var stateEffectImg2:ImageUrlDefine = new ImageUrlDefine();
      
      public var description:String = "";
      
      public var wantDescripB:Boolean = false;
      
      protected var desFleshB:Boolean = false;
      
      protected var _des:String = "";
      
      public var effectInfoArr:Array = [];
      
      private var bulletObj:SkillBulletObj = null;
      
      private var editD:EditIDDefine = null;
      
      public function SkillDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML, father0:String, inXmlB:Boolean = true) : void
      {
         if(inXmlB)
         {
            ClassProperty.inData_byXML(this,xml0,pro_arr);
         }
         this.target.inData_byXML(xml0.target[0]);
         this.father = father0;
         var objStr0:String = String(xml0.obj);
         if(objStr0 != "")
         {
            objStr0 = "{" + objStr0 + "}";
            this.obj = JSON2.decode(objStr0);
         }
         this.baseLabel = this.name;
         if(this.firstCd == -1)
         {
            this.firstCd = this.cd / 3 * 2;
         }
         if(this.intervalT == 0)
         {
            this.intervalT = this.duration;
         }
         if(Boolean(this.passiveSkillArr) && this.passiveSkillArr.length == 0)
         {
            this.passiveSkillArr = null;
         }
         if(Boolean(this.otherConditionArr) && this.otherConditionArr.length == 0)
         {
            this.otherConditionArr = null;
         }
         if(this.father == SkillFather.enemySuper || this.father == SkillFather.noEnemySuper || this.father == SkillFather.heroSkill)
         {
            this.showInLifeBarB = true;
         }
         this.addSkillEffectImg.inData_byXML(xml0.addSkillEffectImg[0]);
         this.meEffectImg.inData_byXML(xml0.meEffectImg[0]);
         this.targetEffectImg.inData_byXML(xml0.targetEffectImg[0]);
         this.pointEffectImg.inData_byXML(xml0.pointEffectImg[0]);
         this.stateEffectImg.inData_byXML(xml0.stateEffectImg[0]);
         this.stateEffectImg2.inData_byXML(xml0.stateEffectImg2[0]);
         this.otherEffectImg.inData_byXML(xml0.otherEffectImg[0]);
         if(this.wantDescripB)
         {
         }
      }
      
      protected function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function getLoopB() : Boolean
      {
         return this.condition == SkillEvent.interval;
      }
      
      public function canCopyB() : Boolean
      {
         if(this.noCopyB)
         {
            return false;
         }
         if(this.meActionLabel != "")
         {
            return false;
         }
         if(this.condition == SkillEvent.add)
         {
            return false;
         }
         if(this.effectType == SkillEffect.no || this.effectType == "")
         {
            return false;
         }
         if(Boolean(this.applyArr) && this.applyArr.length > 0)
         {
            return false;
         }
         if(this.noInClonedB)
         {
            return false;
         }
         if(!this.wantDescripB && !this.showInLifeBarB)
         {
            return false;
         }
         return true;
      }
      
      public function isActiveB() : Boolean
      {
         return this.conditionType == ACTIVE;
      }
      
      public function haveKeyB() : Boolean
      {
         return this.isActiveB() && this.cd < 9999;
      }
      
      public function isPassiveB() : Boolean
      {
         return this.conditionType == PASSIVE;
      }
      
      public function isChangeHurtB() : Boolean
      {
         return this.changeHurtB || this.effectType == SkillEffect.changeHurt;
      }
      
      public function isCharmB() : Boolean
      {
         return this.effectType == "charm" || this.effectType == "coquettish_hero";
      }
      
      public function getIconImgUrl(w0:int, h0:int) : String
      {
         return this.iconUrl36;
      }
      
      public function getBulletObj() : SkillBulletObj
      {
         if(!this.bulletObj)
         {
            this.bulletObj = new SkillBulletObj();
            this.bulletObj.inData_byObj(this.obj);
         }
         return this.bulletObj;
      }
      
      protected function fleshDescription() : Array
      {
         var arr0:Array = null;
         if(!this.desFleshB)
         {
            arr0 = this.getSwapProArr();
            this._des = this.swapProText(arr0,this.description);
            this.desFleshB = true;
            return arr0;
         }
         return null;
      }
      
      protected function getSwapProArr() : Array
      {
         var n:* = undefined;
         var proArr0:Array = null;
         var i:int = 0;
         var obj_pro0:Array = [];
         var loopnum0:int = 0;
         for(n in this.obj)
         {
            obj_pro0[loopnum0] = "obj." + n;
            loopnum0++;
         }
         proArr0 = [];
         for(i = 0; i < 10; i++)
         {
            proArr0.push("effectProArr." + i);
         }
         return pro_arr.concat(obj_pro0).concat(proArr0);
      }
      
      public function getDescription(fleshIfNoB0:Boolean = true) : String
      {
         var s0:String = "";
         if(!this.desFleshB)
         {
            this.fleshDescription();
         }
         if(this._des == "")
         {
            s0 = this.description;
         }
         else
         {
            s0 = this._des;
         }
         if(this.name == "barrenAwnSkill")
         {
            if(Boolean(Gaming.PG.da))
            {
               s0 += ComMethod.color("本周已瞬秒" + Gaming.PG.da.main.save.barren + "次。",GatherColor.purpleColor);
            }
         }
         if(UI_SPECIAL)
         {
            if(this.ignoreSilenceB)
            {
               s0 += ComMethod.color("无视沉默。","#C582DE");
            }
            if(this.ignoreNoSkillB)
            {
               s0 += ComMethod.color("无视封锁。","#DE9E82");
            }
         }
         return s0;
      }
      
      public function getTipStr() : String
      {
         return this.getDescription(true);
      }
      
      protected function swapProText(pro0:Array, str0:String) : String
      {
         var n:* = undefined;
         var name0:String = null;
         for(n in pro0)
         {
            name0 = pro0[n];
            str0 = TextWay.replaceStr(str0,"[" + name0 + "]",this.getProText(name0));
            if(name0 == "mul" || name0 == "secMul")
            {
               str0 = TextWay.replaceStr(str0,"[1-" + name0 + "]",this.getProText(name0,"1-"));
               str0 = TextWay.replaceStr(str0,"[" + name0 + "-1]",this.getProText(name0,"-1"));
               str0 = TextWay.replaceStr(str0,"[" + name0 + "/2]",this.getProText(name0,"/2"));
            }
         }
         str0 = TextWay.replaceStr(str0,"{n}","\n");
         str0 = TextWay.replaceStr(str0,"{","<");
         return TextWay.replaceStr(str0,"}",">");
      }
      
      public function getProText(name0:String, countType0:String = "") : String
      {
         var value0:* = undefined;
         var fatherName0:String = null;
         var fixedNum0:int = 0;
         var perProB0:Boolean = false;
         var str_arr0:Array = name0.split(".");
         if(str_arr0.length > 1)
         {
            fatherName0 = str_arr0[0];
            if(this[fatherName0] is Array)
            {
               value0 = this[fatherName0][int(str_arr0[1])];
            }
            else
            {
               value0 = this[fatherName0][str_arr0[1]];
            }
            if(fatherName0 == "effectProArr")
            {
               perProB0 = true;
            }
         }
         else
         {
            value0 = this[name0];
         }
         if(value0 is Number || value0 is int)
         {
            if(countType0 == "1-")
            {
               value0 = 1 - value0;
            }
            else if(countType0 == "-1")
            {
               value0 -= 1;
            }
            else if(countType0 == "/2")
            {
               value0 /= 2;
            }
         }
         if(perProArr.indexOf(name0) >= 0 || perProB0)
         {
            fixedNum0 = 0;
            if(String(value0).length >= 5)
            {
               fixedNum0 = 1;
            }
            return Number(value0 * 100).toFixed(fixedNum0) + "%";
         }
         return String(value0);
      }
      
      public function getAllImageUrl() : Array
      {
         var name0:String = null;
         var cn0:String = null;
         var i0:ImageUrlDefine = null;
         var bulletDefine0:BulletDefine = null;
         var bodyDefine0:NormalBodyDefine = null;
         var skillName0:String = null;
         var skillDefine0:SkillDefine = null;
         var arr0:Array = [];
         for each(name0 in pro_arr)
         {
            if(this[name0] is ImageUrlDefine)
            {
               i0 = this[name0];
               if(i0.url != "")
               {
                  ComMethod.addNoRepeatInArr(arr0,i0.url);
               }
            }
         }
         if(this.effectType == "bullet")
         {
            bulletDefine0 = Gaming.defineGroup.bullet.getDefine(this.obj["name"]);
            ComMethod.addNoRepeatArrInArr(arr0,bulletDefine0.getAllImageUrl());
         }
         var cnArr0:Array = this.getPreBodyCnNameArr();
         for each(cn0 in cnArr0)
         {
            bodyDefine0 = Gaming.defineGroup.body.getCnDefine(cn0);
            ComMethod.addNoRepeatArrInArr(arr0,bodyDefine0.getAllImageUrl());
         }
         if(this.obj.hasOwnProperty("skillArr"))
         {
            for each(skillName0 in this.obj["skillArr"])
            {
               skillDefine0 = Gaming.defineGroup.skill.getDefine(skillName0);
               ComMethod.addNoRepeatArrInArr(arr0,skillDefine0.getAllImageUrl());
            }
         }
         return arr0;
      }
      
      public function isSummonedUnitsB() : Boolean
      {
         return this.effectType == SkillEffect.summonedUnits || this.summonedUnitsB;
      }
      
      public function getPreBodyCnNameArr() : Array
      {
         var obj0:SkillSummoneObj = null;
         if(this.isSummonedUnitsB())
         {
            obj0 = new SkillSummoneObj();
            obj0.inData_byObj(this.obj);
            return obj0.getAllCnNameArr();
         }
         return [];
      }
      
      public function getBulletNameArr() : Array
      {
         var obj0:SkillBulletObj = null;
         if(this.effectType.indexOf("bullet") == 0)
         {
            obj0 = this.getBulletObj();
            return obj0.getNameArr();
         }
         return [];
      }
      
      public function getAvtiveSkillCd() : Number
      {
         if(this.cd > 2)
         {
            return 2;
         }
         return this.cd;
      }
      
      public function setEditD(d0:EditIDDefine) : void
      {
         this.editD = d0;
      }
      
      public function getEditD() : EditIDDefine
      {
         return this.editD;
      }
      
      public function getBossEditScoreMul() : Number
      {
         var v0:Number = 1.2;
         if(Boolean(this.editD))
         {
            v0 = this.editD.getBossScoreMul(v0);
         }
         return v0;
      }
      
      public function getName() : String
      {
         return this.name;
      }
      
      public function getCnName() : String
      {
         return this.cnName;
      }
      
      public function isFatherHeroB() : Boolean
      {
         return this.father == "heroSkill";
      }
      
      public function isEquipRandomSkillB() : Boolean
      {
         return this.father == SkillFather.headSkill || this.father == SkillFather.coatSkill;
      }
   }
}

