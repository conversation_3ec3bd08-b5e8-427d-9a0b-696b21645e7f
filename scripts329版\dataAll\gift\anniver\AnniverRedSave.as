package dataAll.gift.anniver
{
   import com.sounto.utils.ClassProperty;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class AnniverRedSave
   {
      
      public static var pro_arr:Array = null;
      
      public var haveArr:Array = [];
      
      public var levelArr:Array = [];
      
      public function AnniverRedSave()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.haveArr.length = 0;
      }
      
      public function panHave(name0:String) : Boolean
      {
         return this.haveArr.indexOf(name0) >= 0;
      }
      
      public function setHave(name0:String) : void
      {
         if(!this.panHave(name0))
         {
            this.haveArr.push(name0);
         }
      }
      
      public function panLevel(name0:String) : Boolean
      {
         return this.levelArr.indexOf(name0) >= 0;
      }
      
      public function setLevel(name0:String) : void
      {
         if(!this.panLevel(name0))
         {
            this.levelArr.push(name0);
         }
      }
      
      public function setLevelByGiftArr(garr0:Array) : void
      {
         var g0:GiftAddDefineGroup = null;
         for each(g0 in garr0)
         {
            this.setLevel(g0.name);
         }
      }
   }
}

