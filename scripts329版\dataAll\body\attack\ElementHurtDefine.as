package dataAll.body.attack
{
   import dataAll._base.NormalDefine;
   import dataAll.ui.GatherColor;
   
   public class ElementHurtDefine extends NormalDefine
   {
      
      public var breakShell:String = "";
      
      public var defenceShell:String = "";
      
      public var gatherColor:String = "";
      
      public function ElementHurtDefine()
      {
         super();
      }
      
      public function getGemName() : String
      {
         return name + "Gem";
      }
      
      public function getHurtCn() : String
      {
         return cnName + "伤害";
      }
      
      public function getColor() : String
      {
         return GatherColor.getColor(this.gatherColor);
      }
   }
}

