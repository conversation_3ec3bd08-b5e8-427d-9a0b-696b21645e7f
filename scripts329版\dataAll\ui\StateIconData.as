package dataAll.ui
{
   import com.sounto.oldUtils.ComMethod;
   
   public class StateIconData
   {
      
      public var name:String = "";
      
      public var iconUrl:String = "";
      
      public var time:Number = 0;
      
      public function StateIconData()
      {
         super();
      }
      
      public function getTimeStr() : String
      {
         if(this.time >= 3600)
         {
            return ComMethod.getTimeStr(this.time);
         }
         return ComMethod.getTimeStrTwo(this.time);
      }
   }
}

