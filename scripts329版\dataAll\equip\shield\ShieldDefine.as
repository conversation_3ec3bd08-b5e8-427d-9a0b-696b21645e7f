package dataAll.equip.shield
{
   import com.common.text.TextWay;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.NumberMethod;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipType;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.must.define.MustDefine;
   import dataAll.skill.define.HeroSkillDefine;
   import dataAll.things.define.ThingsDefine;
   
   public class ShieldDefine extends EquipDefine
   {
      
      public static var pro_arr:Array = null;
      
      private var dealSkillB:Boolean = false;
      
      public var icon24:String = "";
      
      public var baseLabel:String = "";
      
      public function ShieldDefine()
      {
         super();
         this.lv = 1;
         this.maxLv = 1;
      }
      
      public function get lv() : Number
      {
         return CF.getAttribute("lv");
      }
      
      public function set lv(v0:Number) : void
      {
         CF.setAttribute("lv",v0);
      }
      
      public function get maxLv() : Number
      {
         return CF.getAttribute("maxLv");
      }
      
      public function set maxLv(v0:Number) : void
      {
         CF.setAttribute("maxLv",v0);
      }
      
      public function get stren() : Number
      {
         return CF.getAttribute("stren");
      }
      
      public function set stren(v0:Number) : void
      {
         CF.setAttribute("stren",v0);
      }
      
      public function get cd() : Number
      {
         return CF.getAttribute("cd");
      }
      
      public function set cd(v0:Number) : void
      {
         CF.setAttribute("cd",v0);
      }
      
      public function get delay() : Number
      {
         return CF.getAttribute("delay");
      }
      
      public function set delay(v0:Number) : void
      {
         CF.setAttribute("delay",v0);
      }
      
      override public function inData_byXML(xml0:XML, father0:String) : void
      {
         father = father0;
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         if(iconLabel == "")
         {
            iconLabel = "equipIcon/" + this.baseLabel;
         }
         if(this.icon24 == "")
         {
            this.icon24 = "equipIcon/" + this.baseLabel + "24";
         }
         if(name == "")
         {
            name = this.baseLabel + "_" + this.lv;
         }
         type = EquipType.SHIELD;
      }
      
      private function fleshDescription() : void
      {
         description = TextWay.fleshDescription(description);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function clone() : ShieldDefine
      {
         var d0:ShieldDefine = new ShieldDefine();
         d0.inData_byObj(this);
         return d0;
      }
      
      public function cloneBase() : ShieldDefine
      {
         var d0:ShieldDefine = this.clone();
         d0.iconLabel = "";
         d0.name = "";
         return d0;
      }
      
      override public function getTrueCnName() : String
      {
         return cnName + this.lv + "级";
      }
      
      override public function getSortIndex() : int
      {
         var index0:int = index;
         var d0:ShieldDefine = Gaming.defineGroup.shield.getDefine(this.baseLabel);
         if(Boolean(d0))
         {
            index0 = d0.index;
         }
         return int(index0 * 1000 + 99 - this.lv);
      }
      
      public function getUpgradeName() : String
      {
         return this.baseLabel + "_" + (this.lv + 1);
      }
      
      public function getUpgradeMustThingsName() : String
      {
         return this.baseLabel;
      }
      
      override public function getComposeThingsDefine() : ThingsDefine
      {
         return Gaming.defineGroup.things.getDefine(this.getUpgradeMustThingsName());
      }
      
      private function getFirstSkill() : String
      {
         return skillArr[0];
      }
      
      public function getSkillDefine() : HeroSkillDefine
      {
         return Gaming.defineGroup.skill.getDefine(this.getFirstSkill()) as HeroSkillDefine;
      }
      
      public function getNextSkillDefine() : HeroSkillDefine
      {
         if(this.lv >= this.maxLv || skillArr.length == 0)
         {
            return null;
         }
         return this.getSkillDefine().getNextDefine();
      }
      
      override public function getResolveGift() : GiftAddDefineGroup
      {
         var all0:GiftAddDefineGroup = null;
         var i:int = 0;
         var chipGift0:GiftAddDefineGroup = null;
         var must0:MustDefine = null;
         var gift0:GiftAddDefineGroup = null;
         if(this.lv >= 2)
         {
            all0 = new GiftAddDefineGroup();
            for(i = 2; i <= this.lv; i++)
            {
               must0 = ShieldDataCreator.getUpgradeMust(this,i - 1);
               gift0 = new GiftAddDefineGroup();
               gift0.inMustDefineOnlyThings(must0);
               all0.merge(gift0);
            }
            chipGift0 = super.getResolveGift();
            all0.merge(chipGift0);
            return all0;
         }
         return super.getResolveGift();
      }
      
      public function getGatherTip() : String
      {
         var str0:String = this.getProGatherStr();
         if(str0 != "")
         {
            str0 += "\n";
         }
         return str0 + getNormalGatherTip();
      }
      
      public function getProGatherStr(titleB0:Boolean = true) : String
      {
         var str0:String = "";
         if(titleB0)
         {
            str0 += "\n<i1>|<blue <b>属性：</b>/>";
         }
         str0 += "\n<gray 护盾强度/>|<yellow " + NumberMethod.toPer(this.stren,0) + "/>";
         str0 += "\n<gray 充能时间/>|<yellow " + this.cd + "秒/>";
         return str0 + ("\n<gray 延迟时间/>|<yellow " + this.delay + "秒/>");
      }
   }
}

