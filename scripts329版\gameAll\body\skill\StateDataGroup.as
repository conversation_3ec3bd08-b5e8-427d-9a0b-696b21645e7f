package gameAll.body.skill
{
   import dataAll.body.attack.HurtData;
   import dataAll.skill.SkillAddData;
   import dataAll.skill.define.SkillDefine;
   import dataAll.skill.define.SkillEvent;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.data.NormalBodyData;
   import gameAll.body.motion.NormalGroundMotion;
   import gameAll.skill.InstantEffectCtrl;
   import gameAll.skill.SkillEffectData;
   
   public class StateDataGroup
   {
      
      public var enabled:Boolean = true;
      
      public var obj:Object = {};
      
      public var BB:IO_NormalBody;
      
      public var mot:NormalGroundMotion;
      
      public var dat:NormalBodyData;
      
      private var haveEffectTypeObj:Object = {};
      
      public var initStateFun:Function = null;
      
      public function StateDataGroup(_BB:IO_NormalBody)
      {
         super();
         this.BB = _BB;
         this.mot = this.BB.getMot();
         this.dat = this.BB.getData();
      }
      
      public function addState_bySkillName(name0:String, producer0:IO_NormalBody, extraValue0:Number = 1) : StateData
      {
         var d0:SkillDefine = Gaming.defineGroup.skill.getDefine(name0);
         return this.addState_bySkill(d0,producer0,extraValue0,null);
      }
      
      public function addState_bySkillEffectData(se0:SkillEffectData) : StateData
      {
         return this.addState_bySkill(se0.define,se0.producer,se0.extraValue,se0.addData,se0.hurtData,se0.skillData);
      }
      
      public function addState_bySkill(d0:SkillDefine, producer0:IO_NormalBody, extraValue0:Number = 1, addData0:SkillAddData = null, hurtData0:HurtData = null, skillData0:SkillData = null) : StateData
      {
         var da0:StateData = null;
         var da1:StateData = this.getState_bySkillName(d0.baseLabel);
         if(da1 is StateData)
         {
            da1.hurtData = hurtData0;
            if(!d0.noReStateB)
            {
               da1.t = 0;
               da1.die = 0;
            }
            if(d0.overlyingB)
            {
               ++da1.overlyingNum;
               this.BB.getAi().extraAI.addState(da1);
            }
            return da1;
         }
         da0 = new StateData();
         da0.inData(d0,addData0);
         da0.BB = this.BB;
         da0.producer = producer0;
         da0.hurtData = hurtData0;
         da0.skillData = skillData0;
         da0.extraValue = extraValue0;
         this.obj[d0.baseLabel] = da0;
         this.BB.getAi().extraAI.addState(da0);
         if(!this.haveEffectTypeObj[da0.effectType])
         {
            this.haveEffectTypeObj[da0.effectType] = 0;
         }
         ++this.haveEffectTypeObj[d0.effectType];
         if(d0.passiveSkillArr is Array)
         {
            this.dat.stateD.addPassiveSkillArr(d0.passiveSkillArr);
         }
         if(producer0 is IO_NormalBody)
         {
            if(producer0.getData().camp != this.dat.camp)
            {
               da0.positiveB = false;
            }
         }
         if(d0.stateEffectImg.getCanShowB(this.BB,da0))
         {
            Gaming.EG.follow.addEffectByState(d0.stateEffectImg,this.BB,da0);
         }
         if(d0.stateEffectImg2.getCanShowB(this.BB,da0))
         {
            Gaming.EG.follow.addEffectByState(d0.stateEffectImg2,this.BB,da0);
         }
         return da0;
      }
      
      public function getState_bySkillName(str0:String) : StateData
      {
         return this.obj[str0];
      }
      
      public function getState_byEffectType(str0:String) : StateData
      {
         var da0:StateData = null;
         for each(da0 in this.obj)
         {
            if(da0.define.effectType == str0)
            {
               return da0;
            }
         }
         return null;
      }
      
      private function getEnemyStateArr() : Array
      {
         var da0:StateData = null;
         var arr0:Array = [];
         for each(da0 in this.obj)
         {
            if(da0.isEnemyB(this.dat.camp))
            {
               arr0.push(da0);
            }
         }
         return arr0;
      }
      
      private function fleshObj() : Object
      {
         var n:* = undefined;
         var da0:StateData = null;
         var obj0:Object = {};
         for(n in this.obj)
         {
            da0 = this.obj[n] as StateData;
            if(da0 is StateData)
            {
               if(da0.die == 0)
               {
                  obj0[n] = da0;
               }
               else
               {
                  this.removeStateEvent(da0);
               }
            }
         }
         return obj0;
      }
      
      public function setEnemyStateTMul(mul0:Number) : int
      {
         var da0:StateData = null;
         var arr0:Array = this.getEnemyStateArr();
         for each(da0 in arr0)
         {
            da0.setEnemyStateTMul(mul0);
         }
         return arr0.length;
      }
      
      public function clearStateByBaseLabelArr(nameArr0:Array) : int
      {
         var n:* = undefined;
         var clearNum0:int = 0;
         var obj0:Object = {};
         for(n in this.obj)
         {
            if(nameArr0.indexOf(n) == -1)
            {
               obj0[n] = this.obj[n];
            }
            else
            {
               this.removeStateEvent(this.obj[n]);
               clearNum0++;
            }
         }
         this.obj = obj0;
         return clearNum0;
      }
      
      public function clearStateByNameContain(contain0:String) : int
      {
         var n:* = undefined;
         var clearNum0:int = 0;
         var obj0:Object = {};
         for(n in this.obj)
         {
            if(n.indexOf(contain0) == -1)
            {
               obj0[n] = this.obj[n];
            }
            else
            {
               this.removeStateEvent(this.obj[n]);
               clearNum0++;
            }
         }
         this.obj = obj0;
         return clearNum0;
      }
      
      public function clearByEffectType(type0:String, mustEnemyB0:Boolean = false) : StateData
      {
         var m_da0:StateData = null;
         var n:* = undefined;
         var da0:StateData = null;
         var delB0:Boolean = false;
         if(!this.haveEffectTypeObj.hasOwnProperty(type0))
         {
            return null;
         }
         if(this.haveEffectTypeObj[type0] < 1)
         {
            return null;
         }
         var obj0:Object = {};
         for(n in this.obj)
         {
            da0 = this.obj[n];
            delB0 = false;
            if(da0.effectType == type0)
            {
               if(mustEnemyB0)
               {
                  delB0 = da0.isEnemyB(this.dat.camp);
               }
               else
               {
                  delB0 = true;
               }
            }
            if(!delB0)
            {
               obj0[n] = da0;
            }
            else
            {
               m_da0 = da0;
               this.removeStateEvent(da0);
            }
         }
         this.obj = obj0;
         return m_da0;
      }
      
      public function clearAllHiding() : void
      {
         this.clearByEffectType("hidingB_hugePosion");
         this.clearByEffectType("Weaver_web_hidingB");
         this.clearByEffectType("hidingB");
      }
      
      public function clearAllHidingCrit(b0:IO_NormalBody, b1:IO_NormalBody, h0:HurtData) : Number
      {
         var crit0:Number = NaN;
         this.clearByEffectType("hidingB_hugePosion");
         this.clearByEffectType("Weaver_web_hidingB");
         var da0:StateData = this.clearByEffectType("hidingB");
         if(Boolean(da0))
         {
            crit0 = da0.getMul();
            if(Boolean(b1) && da0.define.secMul > 0)
            {
               if(b1.getMot().x > b0.getMot().x == !b0.getImg().rightB)
               {
                  crit0 *= 1 + da0.define.secMul;
               }
            }
            return crit0;
         }
         return 0;
      }
      
      public function clearAllEnemyState(noClearPanB0:Boolean = true) : int
      {
         var da0:StateData = null;
         var arr0:Array = this.getEnemyStateArr();
         var num0:int = 0;
         for each(da0 in arr0)
         {
            if(!da0.define.noBeClearB || !noClearPanB0)
            {
               this.removeStateEvent(da0);
               num0++;
            }
         }
         return num0;
      }
      
      public function clearAllWeState(noClearPanB0:Boolean = true) : int
      {
         var da0:StateData = null;
         var num0:int = 0;
         var arr0:Array = [];
         for each(da0 in this.obj)
         {
            if(da0.isWeB(this.dat.camp))
            {
               arr0.push(da0);
            }
         }
         num0 = 0;
         for each(da0 in arr0)
         {
            if(!da0.define.noBeClearB || !noClearPanB0)
            {
               this.removeStateEvent(da0);
               num0++;
            }
         }
         return num0;
      }
      
      public function clearAllExceptAdd() : void
      {
         var da0:StateData = null;
         for each(da0 in this.obj)
         {
            if(da0.define.condition != SkillEvent.add)
            {
               this.removeStateEvent(da0);
            }
         }
      }
      
      public function dieEvent() : void
      {
         this.clearAllCanClear(true);
      }
      
      public function clearAllCanClear(dieB0:Boolean = false) : void
      {
         var n:* = undefined;
         var da0:StateData = null;
         this.initStateFun(dieB0);
         for(n in this.obj)
         {
            da0 = this.obj[n];
            if(da0.die == 0)
            {
               if(!da0.define.everNoClearB)
               {
                  da0.die = 1;
                  --this.haveEffectTypeObj[da0.effectType];
               }
            }
         }
         this.obj = this.fleshObj();
      }
      
      public function clearAll() : void
      {
         var n:* = undefined;
         var da0:StateData = null;
         this.initStateFun();
         for(n in this.obj)
         {
            da0 = this.obj[n];
            da0.die = 1;
         }
         this.obj = {};
         this.haveEffectTypeObj = {};
      }
      
      private function removeStateEvent(da0:StateData) : void
      {
         da0.die = 1;
         this.BB.getAi().extraAI.delState(da0);
         if(da0.define.passiveSkillArr is Array)
         {
            this.dat.stateD.delPassiveSkillArr(da0.define.passiveSkillArr);
         }
         if(da0.define.stateRemoveEvent != "")
         {
            InstantEffectCtrl.doEffect_byWhenStateDataRemove(this.BB,da0);
         }
      }
      
      public function FTimer() : void
      {
         var n:* = undefined;
         var da0:StateData = null;
         var bodyDieB0:Boolean = false;
         if(!this.enabled)
         {
            return;
         }
         this.initStateFun();
         var dieB0:Boolean = false;
         for(n in this.obj)
         {
            da0 = this.obj[n];
            if(da0 is StateData)
            {
               bodyDieB0 = this.BB.getDie() > 0;
               if(!bodyDieB0 || da0.define.everNoClearB)
               {
                  if(da0.define.groundDieB)
                  {
                     if(this.mot.isGroundB())
                     {
                        da0.die = 1;
                     }
                  }
                  if(da0.die > 0)
                  {
                     --this.haveEffectTypeObj[da0.effectType];
                     dieB0 = true;
                  }
                  else
                  {
                     da0.FTimer();
                  }
               }
            }
         }
         if(dieB0)
         {
            this.obj = this.fleshObj();
         }
      }
   }
}

