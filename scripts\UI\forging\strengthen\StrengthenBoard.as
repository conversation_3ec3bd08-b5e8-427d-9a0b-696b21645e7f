package UI.forging.strengthen
{
   import UI.UIOrder;
   import UI.UIShow;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.must.NormalMustBox;
   import UI.base.oneKey.OneKeyCtrl;
   import UI.forging.echelon.EchelonBox;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldNiuBiCF;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.arms.ArmsData;
   import dataAll.arms.creator.ArmsStrengthenCtrl;
   import dataAll.equip.EquipData;
   import dataAll.items.IO_StrengthenData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.creator.ItemsStrengthenCtrl;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.ui.tip.CheckData;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class StrengthenBoard extends NormalUI
   {
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var itemsType:String = "arms";
      
      private var arrowSp:Sprite;
      
      private var pointer:MovieClip;
      
      private var beforeTxt:TextField;
      
      private var beforeGrip:ItemsGrid = new ItemsGrid();
      
      private var beforeSp:MovieClip;
      
      private var afterTxt:TextField;
      
      private var afterGrip:ItemsGrid = new ItemsGrid();
      
      private var afterSp:MovieClip;
      
      private var rateTxt:TextField;
      
      private var mustSp:Sprite;
      
      private var echelonSp:Sprite;
      
      private var btnSp:MovieClip;
      
      private var oneBtnSp:MovieClip;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var oneBtn:NormalBtn = new NormalBtn();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var echelonBox:EchelonBox = new EchelonBox();
      
      public var nowData:IO_StrengthenData = null;
      
      private var successB:Boolean = false;
      
      private var echelonB:Boolean = true;
      
      private var oneB:Boolean = false;
      
      private var numKey:OneKeyCtrl = new OneKeyCtrl();
      
      private var numKeyBtnSp:MovieClip;
      
      private var numKeyBtn:NormalBtn = new NormalBtn();
      
      public function StrengthenBoard()
      {
         super();
         this.beforeLv = 0;
      }
      
      public function get beforeLv() : Number
      {
         return this.CF.getAttribute("beforeLv");
      }
      
      public function set beforeLv(v0:Number) : void
      {
         this.CF.setAttribute("beforeLv",v0);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["rateTxt","arrowSp","pointer","echelonSp","beforeTxt","beforeSp","afterTxt","afterSp","mustSp","btnSp","oneBtnSp","numKeyBtnSp"];
         super.setImg(img0);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("普通强化");
         this.btn.activedAndEnabled = false;
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.btn.addEventListener(MouseEvent.MOUSE_OVER,this.btnOver);
         addChild(this.oneBtn);
         this.oneBtn.setImg(this.oneBtnSp);
         this.oneBtn.setName("百分百强化");
         this.oneBtn.activedAndEnabled = false;
         this.oneBtn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.oneBtn.addEventListener(MouseEvent.MOUSE_OVER,this.btnOver);
         ItemsGripTipCtrl.addNormalBtnTip(this.oneBtn);
         this.oneBtn.tipString = "强化百分百成功，27级以下直接强化到27级，27级及以上提升1个强化等级。";
         addChild(this.beforeGrip);
         this.beforeGrip.setImg(this.beforeSp);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.beforeGrip);
         addChild(this.afterGrip);
         this.afterGrip.setImg(this.afterSp);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.afterGrip);
         addChild(this.arrowSp);
         this.pointer.gotoAndStop(1);
         addChild(this.mustBox);
         this.mustBox.setImg(this.mustSp);
         addChild(this.echelonBox);
         this.echelonBox.setImg(this.echelonSp);
         this.echelonBox.addEventListener(ClickEvent.ON_CLICK,this.echelonClick);
         addChild(this.numKeyBtn);
         this.numKeyBtn.setImg(this.numKeyBtnSp);
         this.numKeyBtn.activedAndEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.numKeyBtn);
         this.numKeyBtn.addEventListener(MouseEvent.CLICK,this.numKeyBtnClick);
         this.numKeyBtn.tipString = "一键强化。\n以下情况自动停止：\n1、强化至" + ArmsStrengthenCtrl.getStrenRanMax() + "级。\n2、材料不足。";
         this.numKey.startFun = this.btnClick;
         this.numKey.endFun = this.numKeyEnd;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,this.itemsType);
         this.showDataAndPan(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
         this.successB = false;
         this.echelonB = false;
      }
      
      public function armsGripClick(da0:ArmsData) : void
      {
         if(visible)
         {
            this.showDataAndPan(da0);
         }
      }
      
      public function equipGripClick(da0:EquipData) : void
      {
         if(visible)
         {
            this.showDataAndPan(da0);
         }
      }
      
      private function showDataAndPan(da0:IO_StrengthenData) : void
      {
         var dg0:ItemsDataGroup = null;
         this.showNone();
         this.pointer.gotoAndStop(this.oneB ? 2 : 1);
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择要强化的武器或装备。");
         if(Boolean(da0))
         {
            dg0 = Gaming.PG.da.findItemsDataFist(da0);
            if(dg0 is ItemsDataGroup)
            {
               this.showData(da0);
            }
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      private function showData(da0:IO_StrengthenData) : void
      {
         var rateStr0:String = null;
         var minLv0:int = 0;
         var echelon_d0:MustDefine = null;
         var overMaxB0:Boolean = false;
         var isMinB0:Boolean = false;
         this.nowData = da0;
         var nowLv0:int = int(da0.getStrengthenLv());
         var after_da0:IO_StrengthenData = ItemsStrengthenCtrl.getAfterData(da0);
         var scale0:Number = this.itemsType == "arms" ? 0.8 : 1;
         this.beforeGrip.inDataByAllItems(da0);
         this.beforeGrip.setIconScale(scale0);
         this.afterGrip.inDataByAllItems(after_da0);
         this.afterGrip.setIconScale(scale0);
         var rate0:Number = ItemsStrengthenCtrl.getSuccessRate(this.nowData,this.oneB);
         var c0:CheckData = ItemsStrengthenCtrl.panStrengthenB(da0,this.oneB);
         this.beforeTxt.visible = c0.bb;
         this.afterTxt.visible = c0.bb;
         this.afterGrip.visible = c0.bb;
         if(c0.bb)
         {
            this.beforeTxt.htmlText = da0.getStrengthenTitle();
            this.afterTxt.htmlText = after_da0.getStrengthenTitle();
            rateStr0 = ComMethod.color("成功率：" + TextWay.numberToPer(rate0,0),"#FFFF00",14);
            if(this.oneB == false)
            {
               if(nowLv0 > ItemsStrengthenCtrl.getSucessLv() && rate0 < 1)
               {
                  minLv0 = ItemsStrengthenCtrl.getMinLv(da0);
                  rateStr0 += "\n强化失败后等级不低于" + minLv0 + "级";
               }
            }
            this.rateTxt.htmlText = rateStr0;
         }
         else
         {
            this.rateTxt.htmlText = ComMethod.color(c0.info,"#FF0000");
         }
         var must_d0:MustDefine = ItemsStrengthenCtrl.getMust(da0,this.oneB);
         var bodyLv0:int = 0;
         if(this.nowData.getPlayerData() is NormalPlayerData)
         {
            bodyLv0 = int(this.nowData.getPlayerData().level);
         }
         var mustB0:Boolean = this.mustBox.inData(must_d0,bodyLv0);
         this.mustBox.setCoinVisible(this.oneB);
         var echelonB0:Boolean = true;
         this.echelonBox.visible = !this.oneB;
         if(!this.oneB)
         {
            echelon_d0 = ItemsStrengthenCtrl.getEchelonMust(da0);
            overMaxB0 = nowLv0 > ItemsStrengthenCtrl.getEchelonMaxLevel();
            isMinB0 = nowLv0 <= ItemsStrengthenCtrl.getMinLv(this.nowData) && nowLv0 > ItemsStrengthenCtrl.getSucessLv();
            echelonB0 = this.echelonBox.inData(echelon_d0,Gaming.PG.save.setting.echelonB,overMaxB0,isMinB0);
         }
         var canB0:Boolean = c0.bb;
         if(this.oneB)
         {
            this.btn.actived = false;
            this.oneBtn.actived = canB0 && mustB0;
         }
         else
         {
            this.oneBtn.actived = false;
            this.btn.actived = canB0 && mustB0 && echelonB0;
         }
         this.numKeyBtn.actived = this.btn.actived && da0.getStrengthenLv() < ArmsStrengthenCtrl.getStrenRanMax();
      }
      
      private function showNone() : void
      {
         this.nowData = null;
         this.beforeGrip.clearData();
         this.afterGrip.clearData();
         this.beforeTxt.htmlText = "";
         this.afterTxt.htmlText = "";
         this.rateTxt.htmlText = "";
         this.mustBox.setShowState(false);
         this.btn.actived = false;
         this.numKeyBtn.actived = this.btn.actived;
         this.oneBtn.actived = false;
      }
      
      private function echelonClick(e:ClickEvent) : void
      {
         Gaming.PG.save.setting.setEchelon();
         this.showDataAndPan(this.nowData);
      }
      
      private function btnOver(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var oneB0:Boolean = btn0 == this.oneBtn;
         if(this.numKey.ingB && oneB0)
         {
            return;
         }
         if(this.oneB != oneB0)
         {
            this.oneB = oneB0;
            this.showDataAndPan(this.nowData);
            Gaming.soundGroup.playSound("uiSound","changeLabel");
         }
      }
      
      private function btnClick(e:MouseEvent = null) : void
      {
         var btn0:NormalBtn = this.btn;
         if(Boolean(e))
         {
            btn0 = e.target as NormalBtn;
         }
         if(btn0.actived)
         {
            this.oneB = btn0 == this.oneBtn;
            if(Boolean(this.nowData))
            {
               if(this.numKey.ingB && this.nowData.getStrengthenLv() >= ArmsStrengthenCtrl.getStrenRanMax())
               {
                  this.stopNumKey();
               }
               else
               {
                  PlayerMustCtrl.deductMust(this.getMust(),this.afterStrengthen,this.stopNumKey);
               }
            }
            else
            {
               this.numKey.doErrorOrAlert("数据不存在！");
            }
         }
         else
         {
            this.stopNumKey();
         }
      }
      
      private function getMust() : MustDefine
      {
         var must_d0:MustDefine = ItemsStrengthenCtrl.getMust(this.nowData,this.oneB);
         if(!this.oneB && Gaming.PG.save.setting.echelonB)
         {
            must_d0.addThingsDataByArr(ItemsStrengthenCtrl.getEchelonMust(this.nowData).getThingsStrArr());
         }
         return must_d0;
      }
      
      private function afterStrengthen() : void
      {
         this.numKey.useMust(this.getMust());
         this.beforeLv = this.nowData.getStrengthenLv();
         var rate0:Number = ItemsStrengthenCtrl.getSuccessRate(this.nowData,this.oneB);
         this.successB = Math.random() <= rate0;
         if(this.beforeLv >= 15)
         {
            this.nowData.strengthenNumAdd();
         }
         if(this.successB)
         {
            ItemsStrengthenCtrl.strengthenOne(this.nowData,this.oneB);
         }
         else
         {
            this.echelonB = this.nowData.canEchelonB();
            if(!this.echelonB)
            {
               ItemsStrengthenCtrl.downStrengthenOne(this.nowData);
            }
         }
         Gaming.PG.save.getCount().strengthEvent(this.nowData);
         if(this.oneB)
         {
            this.yes_save();
         }
         else if(this.numKey.ingB)
         {
            this.nextNumKey(rate0 < 1);
         }
         else if(rate0 >= 1)
         {
            this.yes_save();
         }
         else
         {
            UIOrder.save(true,true,false,this.yes_save,UIShow.hideNowApp,false,true);
         }
      }
      
      private function yes_save(v:* = null) : void
      {
         var error0:String = null;
         this.showDataAndPan(this.nowData);
         Gaming.uiGroup.allBagUI.fleshAllBox();
         var clv0:int = this.nowData.getStrengthenLv() - this.beforeLv;
         if(this.successB)
         {
            Gaming.uiGroup.alertBox.showSuccess("强化成功！\n物品强化等级提升<b>" + ComMethod.color(clv0 + "","#00FF00") + "级。</b>");
         }
         else
         {
            error0 = "强化失败！";
            if(clv0 < 0)
            {
               error0 += "\n物品强化等级下降" + -clv0 + "级。";
            }
            Gaming.uiGroup.alertBox.showError(error0);
         }
      }
      
      private function numKeyBtnClick(e:MouseEvent) : void
      {
         if(!this.oneB && this.numKeyBtn.actived)
         {
            Gaming.uiGroup.alertBox.showNumChoose("选择强化次数",1,500,1,1,this.yesNumKey);
         }
      }
      
      private function yesNumKey(num0:int) : void
      {
         if(Boolean(this.nowData))
         {
            Gaming.uiGroup.connectUI.show("数据处理中……",1);
            this.numKey.start(num0,this.nowData.getStrengthenLv());
         }
      }
      
      private function numKeyEnd() : void
      {
         if(this.numKey.getSaveB())
         {
            UIOrder.save(true,true,false,this.yes_numKeyEnd,UIShow.hideNowApp,false,true);
         }
         else
         {
            this.yes_numKeyEnd();
         }
      }
      
      private function yes_numKeyEnd(v:* = null) : void
      {
         var tip0:String = null;
         Gaming.uiGroup.connectUI.hide();
         if(this.numKey.getNum() > 0)
         {
            this.showDataAndPan(this.nowData);
            Gaming.uiGroup.allBagUI.fleshAllBox();
            tip0 = "强化等级变化：" + this.numKey.getLevelChangeStr();
            tip0 += "\n强化次数：" + this.numKey.getNum() + "次";
            if(Boolean(this.numKey.getGift()))
            {
               tip0 += "\n消耗材料：" + this.numKey.getGift().getDescription();
            }
            Gaming.uiGroup.alertBox.showSuccess(tip0);
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("当前无法一键强化。");
         }
         this.numKey.clear();
      }
      
      private function nextNumKey(saveB0:Boolean) : void
      {
         this.showDataAndPan(this.nowData);
         this.numKey.next(this.nowData.getStrengthenLv(),saveB0);
      }
      
      private function stopNumKey(v0:* = null) : void
      {
         this.numKey.doErrorIng("强化中止！");
      }
   }
}

