package dataAll.equip.device
{
   import dataAll.equip.define.EquipDefine;
   
   public class DeviceDefineGroup
   {
      
      private var nowIndex:int = 0;
      
      public var obj:Object = {};
      
      public var fatherArrObj:Object = {};
      
      public var arr:Array = [];
      
      public var baseNameArr:Array = [];
      
      public function DeviceDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var x0:XML = null;
         var father0:String = null;
         var xl2:XMLList = null;
         var x2:XML = null;
         var d0:DeviceDefine = null;
         var xl0:XMLList = xml0.father;
         for each(x0 in xl0)
         {
            father0 = x0.@name;
            xl2 = x0.equip;
            for each(x2 in xl2)
            {
               d0 = new DeviceDefine();
               d0.inData_byXML(x2,father0);
               this.obj[d0.name] = d0;
               this.arr.push(d0);
               this.addInFatherArr(d0,father0);
               if(!d0.rareB)
               {
                  this.baseNameArr.push(d0.name);
               }
               Gaming.defineGroup.dropItems.inDeviceDefine(d0);
            }
         }
         this.addNew();
      }
      
      private function addInFatherArr(d0:EquipDefine, father0:String) : void
      {
         d0.index = this.nowIndex;
         ++this.nowIndex;
         if(!this.fatherArrObj.hasOwnProperty(father0))
         {
            this.fatherArrObj[father0] = [];
         }
         this.fatherArrObj[father0].push(d0);
      }
      
      public function getDefine(name0:String) : DeviceDefine
      {
         return this.obj[name0];
      }
      
      public function getNormalBaseNameArr() : Array
      {
         return this.baseNameArr;
      }
      
      private function addNew() : void
      {
         var d0:DeviceDefine = null;
         var max0:int = 0;
         var i:int = 0;
         var d2:DeviceDefine = null;
         var arr0:Array = this.arr.concat([]);
         for each(d0 in arr0)
         {
            max0 = d0.getMaxLevel();
            for(i = 1; i <= max0; i++)
            {
               d2 = d0.clone();
               d2.name += "_" + i;
               d2.skill += "_" + i;
               d2.lv = i;
               d2.baseLabel = d0.name;
               this.obj[d2.name] = d2;
            }
         }
      }
      
      public function getRandomDropName() : String
      {
         var d0:DeviceDefine = null;
         var arr0:Array = [];
         for each(d0 in this.arr)
         {
            if(!d0.rareB)
            {
               arr0.push(d0);
            }
         }
         d0 = arr0[int(Math.random() * arr0.length)];
         return d0.getUpgradeMustName();
      }
      
      public function getDeviceDefineByCn(cn0:String) : DeviceDefine
      {
         var d0:DeviceDefine = null;
         for each(d0 in this.obj)
         {
            if(d0.cnName == cn0 && d0.lv == 1)
            {
               return d0;
            }
         }
         return null;
      }
      
      public function getIconNum() : int
      {
         var d0:EquipDefine = null;
         var obj0:Object = {};
         var num0:int = 0;
         for each(d0 in this.obj)
         {
            if(!obj0.hasOwnProperty(d0.iconLabel))
            {
               obj0[d0.iconLabel] = 0;
               num0++;
            }
         }
         return num0;
      }
   }
}

