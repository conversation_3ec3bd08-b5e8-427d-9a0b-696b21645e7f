package com.sounto.oldUtils
{
   public class CalendarDefine
   {
      
      public function CalendarDefine()
      {
         super();
      }
      
      public static function getDateName(year0:Number, month0:int) : int
      {
         var date0:Date = new Date(year0,month0 - 1,28);
         for(var i:int = 29; i <= 31; i++)
         {
            date0.setDate(i);
            if(date0.month != month0 - 1)
            {
               return i - 1;
            }
         }
         return 31;
      }
      
      public static function getDateStringArrBefore(year0:Number, month0:int, date0:int) : Array
      {
         var da0:Date = null;
         var sd0:StringDate = null;
         var arr0:Array = [];
         for(var i:int = 1; i < date0; i++)
         {
            da0 = new Date(year0,month0 - 1,i);
            sd0 = new StringDate();
            sd0.inData_byObj(da0);
            arr0.push(sd0.getDateStr());
         }
         return arr0;
      }
   }
}

