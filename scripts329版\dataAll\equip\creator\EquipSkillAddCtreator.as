package dataAll.equip.creator
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll.equip.save.EquipSave;
   import dataAll.skill.define.HeroSkillDefine;
   import dataAll.ui.GatherColor;
   
   public class EquipSkillAddCtreator
   {
      
      public function EquipSkillAddCtreator()
      {
         super();
      }
      
      public static function addSkillAddData(s0:EquipSave) : void
      {
         var heroSkillDefine0:HeroSkillDefine = null;
         var v0:Number = NaN;
         var pro0:String = null;
         var lv0:int = s0.getTrueLevel();
         var obj0:Object = {};
         var arr0:Array = Gaming.defineGroup.skill.getRandomAddDefineArr(lv0,1);
         for each(heroSkillDefine0 in arr0)
         {
            v0 = heroSkillDefine0.addD.getRandomValue();
            pro0 = heroSkillDefine0.baseLabel;
            obj0[pro0] = v0;
         }
         s0.heroSkillAddObj = obj0;
      }
      
      public static function getGatherTip(obj0:Object) : String
      {
         var name0:String = null;
         var str0:String = "";
         var proNum0:int = 0;
         for(name0 in obj0)
         {
            proNum0++;
            str0 += "\n" + getOnePro(name0,obj0[name0]);
         }
         if(proNum0 == 0)
         {
            str0 = "";
         }
         else
         {
            str0 = "\n<i1>|<blue <b>人物技能附加属性：</b>/>" + str0 + "\n";
         }
         return str0;
      }
      
      public static function getProTip(obj0:Object) : String
      {
         var name0:String = null;
         var v0:Number = NaN;
         var d0:HeroSkillDefine = null;
         var valueStr0:String = null;
         var str0:String = "";
         for(name0 in obj0)
         {
            if(str0 != "")
            {
               str0 += "\n";
            }
            v0 = Number(obj0[name0]);
            d0 = Gaming.defineGroup.skill.getOriginalHeroDefine(name0);
            valueStr0 = getValueString(d0.addD.pro,v0,d0.addD.fixedNum);
            str0 += ComMethod.color(d0.cnName,GatherColor.yellowColor) + "：";
            str0 += ComMethod.color(d0.addD.info.replace("[v]",valueStr0),GatherColor.orangeColor);
         }
         return str0;
      }
      
      public static function getOnePro(baseLabel0:String, v0:Number, haveSkillNameB0:Boolean = true) : String
      {
         var d0:HeroSkillDefine = Gaming.defineGroup.skill.getOriginalHeroDefine(baseLabel0);
         var valueStr0:String = getValueString(d0.addD.pro,v0,d0.addD.fixedNum);
         var str0:String = "";
         if(haveSkillNameB0)
         {
            str0 += "<orange " + d0.cnName + "：/>";
         }
         return str0 + ("<orange " + d0.addD.info.replace("[v]",valueStr0) + "/>");
      }
      
      private static function getValueString(proName0:String, value0:Number, fixedNum0:int) : String
      {
         var first0:String = value0 > 0 ? "+" : "";
         if(proName0 == "mul")
         {
            return first0 + Number(value0 * 100).toFixed(fixedNum0) + "%";
         }
         return first0 + Number(value0.toFixed(fixedNum0));
      }
   }
}

