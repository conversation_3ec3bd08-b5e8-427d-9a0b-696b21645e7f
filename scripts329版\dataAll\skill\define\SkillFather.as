package dataAll.skill.define
{
   public class SkillFather
   {
      
      public static const heroSkill:String = "heroSkill";
      
      public static const deviceSkill:String = "deviceSkill";
      
      public static const enemySuper:String = "enemySuper";
      
      public static const noEnemySuper:String = "noEnemySuper";
      
      public static const headSkill:String = "headSkill";
      
      public static const coatSkill:String = "coatSkill";
      
      public static const fashionSkill:String = "fashionSkill";
      
      public static const charmNoSkillArr:Array = ["skillCopyTransport","MeatySkillBack","skillCopy_enemy","deadlyArrow","deadlyGhost","revengeGhost","revengeArrow","corpsePoison","ruleRange","toLand","offAllSkill"];
      
      public static const noBeCharmIfHaveSkill:Array = ["MeatySkillBack"];
      
      public static const likeMissleNoArr:Array = ["likeMissle_Shapers","likeMissleNo","likeMissleNo2"];
      
      public static const flyArr:Array = ["goldFalcon","dragonHeadSkill"];
      
      public static const enemyToArr:Array = ["enemyToZombie","enemyToSpider"];
      
      public static const noWeaponArr:Array = ["bladeShield","weaponDefence"];
      
      public function SkillFather()
      {
         super();
      }
   }
}

