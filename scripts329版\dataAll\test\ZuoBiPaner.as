package dataAll.test
{
   import dataAll._app.goods.define.CrrencyGroup;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._app.goods.define.PriceType;
   import dataAll._player.PlayerData;
   import dataAll.arms.ArmsData;
   import dataAll.equip.EquipData;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.save.ItemsSave;
   import dataAll.things.ThingsData;
   import dataAll.things.define.ThingsDefine;
   import flash.utils.getTimer;
   
   public class ZuoBiPaner
   {
      
      public var PD:PlayerData;
      
      public var specialB:Boolean = true;
      
      public function ZuoBiPaner()
      {
         super();
      }
      
      public static function getStrByFunArr(arr0:Array) : String
      {
         var fun0:Function = null;
         var tt0:Number = NaN;
         var str2:String = null;
         var str0:String = "";
         for each(fun0 in arr0)
         {
            tt0 = getTimer();
            str2 = fun0();
            INIT.TRACE(fun0 + "耗时：" + (getTimer() - tt0));
            if(str2 != "")
            {
               str0 += str2 + ",";
            }
         }
         return str0;
      }
      
      public function zuobiPan() : String
      {
         var fun0:Function = null;
         var str1:String = null;
         if(this.PD.main.save.zuobiReason.indexOf("skill") >= 0)
         {
            this.PD.main.save.isZuobiB = false;
         }
         var str0:String = "";
         var funArr0:Array = [this.goodsMd5,this.blackEquip];
         for each(fun0 in funArr0)
         {
            str1 = fun0();
            if(str1 != "")
            {
               str0 += str1 + ",";
            }
         }
         if(str0 != "")
         {
            return str0;
         }
         if(Gaming.api.save.isLocal())
         {
            return "";
         }
         var cgArr0:Array = this.getCrrencyGroupArr();
         var c0:CrrencyGroup = this.summationCrrencyGroupArr(cgArr0);
         var totalRecharged0:int = this.PD.main.totalRecharged;
         var money0:int = c0.getValue(PriceType.MONEY);
         if(totalRecharged0 < money0 / 2)
         {
            return "所需黄金不足：" + totalRecharged0 + "《" + money0 + "/2";
         }
         return "";
      }
      
      private function getNoPanB() : Boolean
      {
         var noSpecialB0:Boolean = Gaming.testCtrl.canCheatingB();
         if(!this.specialB && !noSpecialB0)
         {
            return true;
         }
         return false;
      }
      
      private function lifeAndDps() : String
      {
         var str0:String = "生命值和战斗力不匹配";
         if(this.PD.main.save.zuobiReason.indexOf(str0) >= 0)
         {
            this.PD.main.save.isZuobiB = false;
         }
         return "";
      }
      
      private function arenaNum() : String
      {
         return "";
      }
      
      private function blackEquip() : String
      {
         var da0:EquipData = null;
         var str0:String = null;
         var arr0:Array = this.PD.getEquipDataArr(false,true,true,true);
         for each(da0 in arr0)
         {
            str0 = Gaming.defineGroup.equipCreator.propertyCtreator.zuobiPanBlackEquipSave(da0.save);
            if(str0 != "")
            {
               return str0;
            }
         }
         return "";
      }
      
      private function blackChip() : String
      {
         var blackEquipNum0:int = 0;
         var arr0:Array = null;
         var da0:EquipData = null;
         var blackChipNum0:int = 0;
         var arr2:Array = null;
         var tda0:ThingsData = null;
         var allNum0:int = 0;
         var trueDropNum0:int = 0;
         var td0:ThingsDefine = null;
         if(this.getNoPanB())
         {
            if(this.PD.main.save.zuobiReason.indexOf("黑色碎片数量") >= 0)
            {
               this.PD.main.save.isZuobiB = false;
            }
            return "";
         }
         blackEquipNum0 = 0;
         arr0 = this.PD.getEquipDataArr(true,true,true,true);
         for each(da0 in arr0)
         {
            if(da0.save.itemsLevel < 86 && da0.save.color == "black")
            {
               blackEquipNum0++;
            }
         }
         blackChipNum0 = 0;
         arr2 = this.PD.thingsBag.dataArr;
         for each(tda0 in arr2)
         {
            td0 = tda0.save.getDefine();
            if(td0.isBlackChip() && td0.itemsLevel < 86)
            {
               blackChipNum0++;
            }
         }
         allNum0 = blackEquipNum0 * 650;
         trueDropNum0 = Gaming.PG.da.drop.save.allBlackChipNum;
         if(allNum0 > trueDropNum0 * 1.5)
         {
            return "黑色碎片数量" + allNum0 + "》" + trueDropNum0;
         }
         return "";
      }
      
      private function goodsMd5() : String
      {
         var bb0:Boolean = Gaming.defineGroup.isGoodsMatchingB();
         if(!bb0)
         {
            return "商品xml被修改！";
         }
         return "";
      }
      
      public function getPartsNum90Gather(haveChildB:Boolean) : Number
      {
         var v0:Number = this.getPartsNum90(haveChildB);
         return Math.floor(v0 / 6);
      }
      
      public function getPartsNum90(haveChildB:Boolean) : Number
      {
         var numArms0:Number = this.getArmsPartsNum90(haveChildB);
         var numThings0:Number = this.getThingsPartsNum90(haveChildB);
         return numArms0 + numThings0;
      }
      
      private function getArmsPartsNum90(haveChildB:Boolean) : Number
      {
         var da0:ArmsData = null;
         var partsArr0:Array = null;
         var things0:ThingsData = null;
         var v0:Number = NaN;
         var armsArr0:Array = this.PD.getArmsDataArr(true,true,true,true);
         var num0:Number = 0;
         for each(da0 in armsArr0)
         {
            partsArr0 = da0.partsData.dataArr;
            for each(things0 in partsArr0)
            {
               v0 = this.countPartsNum90(things0,haveChildB);
               num0 += v0;
            }
         }
         return Math.floor(num0);
      }
      
      private function getThingsPartsNum90(haveChildB:Boolean) : Number
      {
         var da0:ThingsData = null;
         var v0:Number = NaN;
         var arr0:Array = this.PD.partsBag.dataArr;
         var num0:Number = 0;
         for each(da0 in arr0)
         {
            v0 = this.countPartsNum90(da0,haveChildB);
            num0 += v0;
         }
         return Math.floor(num0);
      }
      
      private function countPartsNum90(da0:ThingsData, haveChildB:Boolean) : Number
      {
         var lv0:int = 0;
         var v0:Number = NaN;
         var d0:ThingsDefine = da0.save.getDefine();
         var num0:Number = 0;
         if(d0.isPartsB())
         {
            lv0 = da0.save.getTrueLevel();
            v0 = 0;
            if(lv0 >= 90)
            {
               v0 = 1;
            }
            if(haveChildB)
            {
               if(lv0 == 87)
               {
                  v0 = 1 / 4;
               }
               if(lv0 == 84)
               {
                  v0 = 1 / 4 / 3;
               }
               if(lv0 == 81)
               {
                  v0 = 1 / 4 / 3 / 6;
               }
               if(lv0 == 78)
               {
                  v0 = 1 / 4 / 3 / 6 / 6;
               }
               if(lv0 == 75)
               {
                  v0 = 1 / 4 / 3 / 6 / 6 / 6;
               }
               if(lv0 == 72)
               {
                  v0 = 1 / 4 / 3 / 6 / 6 / 6 / 6;
               }
               if(lv0 == 69)
               {
                  v0 = 1 / 4 / 3 / 6 / 6 / 6 / 6 / 6;
               }
            }
            num0 = v0 * da0.getNowNum();
         }
         return num0;
      }
      
      public function getSpecialPartsNum() : Number
      {
         var da0:ArmsData = null;
         var thingsArr0:Array = null;
         var partsArr0:Array = null;
         var v0:Number = NaN;
         var armsArr0:Array = this.PD.getArmsDataArr(true,true,true,true);
         var sum0:Number = 0;
         for each(da0 in armsArr0)
         {
            partsArr0 = da0.partsData.dataArr;
            v0 = this.countThingsSpecialParts(partsArr0);
            sum0 += v0;
         }
         thingsArr0 = this.PD.partsBag.dataArr;
         sum0 += this.countThingsSpecialParts(thingsArr0);
         return Math.floor(sum0);
      }
      
      private function countThingsSpecialParts(dataArr0:Array) : Number
      {
         var da0:ThingsData = null;
         var d0:ThingsDefine = null;
         var v0:Number = NaN;
         var lv0:int = 0;
         var sum0:Number = 0;
         for each(da0 in dataArr0)
         {
            d0 = da0.save.getDefine();
            v0 = 0;
            if(d0.isPartsSpecialB())
            {
               lv0 = da0.save.getTrueLevel();
               if(lv0 == 3)
               {
                  v0 = 3 * 50;
               }
               else if(lv0 == 2)
               {
                  v0 = 50;
               }
               else
               {
                  v0 = 1;
               }
            }
            sum0 += v0 * da0.getNowNum();
         }
         return sum0;
      }
      
      public function getTextByCrrency() : String
      {
         var cgArr0:Array = this.getCrrencyGroupArr();
         return this.getTextByCrrencyGroupArr(cgArr0);
      }
      
      private function havePan(arr0:Array) : String
      {
         var c0:CrrencyGroup = null;
         var d0:GoodsDefine = null;
         var str0:String = "";
         for each(c0 in arr0)
         {
            d0 = c0.goodsDefine;
            if(Boolean(d0))
            {
               if(d0.priceType == PriceType.MONEY)
               {
                  if(!this.PD.getSave().pay.obj.hasOwnProperty(d0.propId))
                  {
                     str0 += ",【" + d0.cnName + "】无记录";
                  }
               }
            }
         }
         return str0;
      }
      
      private function getCrrencyGroupArr() : Array
      {
         var dg0:ItemsDataGroup = null;
         var da0:IO_ItemsData = null;
         var s0:ItemsSave = null;
         var c0:CrrencyGroup = null;
         var tt0:Number = getTimer();
         var arr0:Array = [];
         var dgArr0:Array = this.PD.getAllItemsDataGroupArr();
         for each(dg0 in dgArr0)
         {
            for each(da0 in dg0.dataArr)
            {
               s0 = da0.getSave();
               c0 = s0.getCrrencyGroup();
               if(c0 is CrrencyGroup)
               {
                  arr0.push(c0);
               }
            }
         }
         trace("getCrrencyGroupArr()耗时：" + (getTimer() - tt0));
         return arr0;
      }
      
      private function summationCrrencyGroupArr(arr0:Array) : CrrencyGroup
      {
         var c2:CrrencyGroup = null;
         var c0:CrrencyGroup = new CrrencyGroup();
         for each(c2 in arr0)
         {
            c0.addGroup(c2);
         }
         return c0;
      }
      
      private function getTextByCrrencyGroupArr(arr0:Array) : String
      {
         var c0:CrrencyGroup = null;
         var str2:String = null;
         var d0:GoodsDefine = null;
         var str0:String = null;
         var allStr0:String = "";
         var strObj0:Object = {};
         for each(c0 in arr0)
         {
            d0 = c0.goodsDefine;
            if(Boolean(d0))
            {
               if(!strObj0[d0.priceType])
               {
                  strObj0[d0.priceType] = "";
               }
               str0 = strObj0[d0.priceType];
               str0 += c0.info + "\n";
               strObj0[d0.priceType] = str0;
            }
         }
         for each(str2 in strObj0)
         {
            allStr0 += str2 + "\n\n";
         }
         return allStr0;
      }
   }
}

