package com.sounto.other
{
   public class OneTimer
   {
      
      public var enabled:Boolean = false;
      
      public var t:int = 0;
      
      public var m:int = -1;
      
      public var m2:int = -1;
      
      public var fun:Function;
      
      public function OneTimer(_m:int)
      {
         super();
         this.m = _m;
         this.m2 = _m;
      }
      
      public function random() : void
      {
         this.m = this.m2 * (Math.random() + 0.5);
      }
      
      public function full() : void
      {
         this.t = this.m;
      }
      
      public function init() : void
      {
         this.t = 0;
      }
      
      public function FTimer() : void
      {
         if(this.enabled)
         {
            if(this.t >= this.m)
            {
               this.t = 0;
               if(this.fun is Function)
               {
                  this.fun();
               }
            }
            else
            {
               ++this.t;
            }
         }
      }
   }
}

