package dataAll.arms.save
{
   import com.sounto.utils.ClassProperty;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.items.save.ItemsSaveGroup;
   
   public class ArmsSaveGroup extends ItemsSaveGroup
   {
      
      public static var pro_arr:Array = [];
      
      public function ArmsSaveGroup()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         super.inData_byObjAndClass(obj0,ArmsSave);
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function getArmsImageArr() : Array
      {
         var n:* = undefined;
         var s0:ArmsSave = null;
         var arr2:Array = [];
         for(n in arr)
         {
            s0 = arr[n];
            arr2.push(s0.getImgLabel());
         }
         return arr2;
      }
      
      public function getArmsSoundUrlArr() : Array
      {
         var n:* = undefined;
         var s0:ArmsSave = null;
         var arr2:Array = [];
         for(n in arr)
         {
            s0 = arr[n];
            arr2.push(s0.shootSoundUrl);
         }
         return arr2;
      }
      
      public function getDefineArr() : Array
      {
         var n:* = undefined;
         var s0:ArmsSave = null;
         var d0:ArmsRangeDefine = null;
         var arr2:Array = [];
         for(n in arr)
         {
            s0 = arr[n];
            d0 = Gaming.defineGroup.bullet.getArmsRangeDefine(s0.name);
            if(d0 is ArmsRangeDefine)
            {
               arr2.push(d0.def);
            }
         }
         return arr2;
      }
   }
}

