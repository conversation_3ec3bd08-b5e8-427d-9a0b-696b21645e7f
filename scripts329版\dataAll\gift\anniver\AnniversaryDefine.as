package dataAll.gift.anniver
{
   import com.sounto.cf.StringCF;
   import com.sounto.oldUtils.StringDate;
   
   public class AnniversaryDefine
   {
      
      private var codeCF:StringCF = new StringCF();
      
      private var signActivityB:Boolean = false;
      
      public function AnniversaryDefine()
      {
         super();
      }
      
      public function get signStart() : String
      {
         return this.codeCF.getAttribute("signStart") as String;
      }
      
      public function set signStart(str0:String) : void
      {
         this.codeCF.setAttribute("signStart",str0);
      }
      
      public function get signEnd() : String
      {
         return this.codeCF.getAttribute("signEnd") as String;
      }
      
      public function set signEnd(str0:String) : void
      {
         this.codeCF.setAttribute("signEnd",str0);
      }
      
      public function init() : void
      {
         this.signStart = "2024-1-17";
         this.signEnd = "2024-2-17 23:59:59";
      }
      
      public function getSignTimeStr() : String
      {
         return "活动时间：" + this.signStart + " ~ " + this.signEnd;
      }
      
      public function countSignActivityTime(time0:String) : void
      {
         var da0:StringDate = new StringDate(time0);
         this.signActivityB = da0.betweenIn(this.signStart,this.signEnd) == 0;
      }
      
      public function isSignActivityBB() : Boolean
      {
         return this.signActivityB;
      }
   }
}

