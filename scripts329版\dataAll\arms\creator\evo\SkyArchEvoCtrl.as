package dataAll.arms.creator.evo
{
   import dataAll.arms.ArmsData;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.save.ArmsSave;
   import dataAll.equip.define.EquipColor;
   import dataAll.must.define.MustDefine;
   
   public class SkyArchEvoCtrl extends DiyArmsEvoCtrl
   {
      
      public function SkyArchEvoCtrl()
      {
         super();
      }
      
      override public function getCnName(cn0:String, evoLv0:int, d0:ArmsDefine) : String
      {
         if(evoLv0 == 2)
         {
            return "超凡·" + cn0;
         }
         return cn0;
      }
      
      override public function getHurtMul(evoLv0:int, d0:ArmsDefine) : Number
      {
         if(evoLv0 == 2)
         {
            return 2;
         }
         return 1;
      }
      
      override public function doEvo(da0:ArmsData) : void
      {
         var s0:ArmsSave = da0.save;
         var d0:ArmsDefine = s0.getArmsRangeDefine().def;
         if(s0.evoLv == 1)
         {
            s0.color = EquipColor.DARKGOLD;
            s0.doEvo();
            s0.armsImgLabel = d0.getImageLabelByBodySuffix(s0.evoLv + "");
         }
      }
      
      override public function getMust(da0:ArmsData) : MustDefine
      {
         var thingArr0:Array = [];
         var d0:MustDefine = new MustDefine();
         d0.coin = 0;
         thingArr0.push(da0.getChipName() + ";200");
         thingArr0.push("frozenGem;200");
         thingArr0.push("yearPig;80");
         thingArr0.push("yearMouse;80");
         thingArr0.push("yearSnake;80");
         d0.inThingsDataByArr(thingArr0);
         return d0;
      }
   }
}

