package com.sounto.process
{
   public class FunProcessFun
   {
      
      protected var nameArr:Array = [];
      
      public var maxTime:Number = 30;
      
      public var proPer:Number = 0;
      
      protected var loopNum:int = -1;
      
      public function FunProcessFun()
      {
         super();
      }
      
      public function loopFun(n:int) : void
      {
      }
      
      public function setNameArr(arr0:Array) : void
      {
         this.nameArr = arr0;
      }
      
      public function getLoopNum() : int
      {
         if(this.loopNum == -1)
         {
            return this.nameArr.length;
         }
         return this.loopNum;
      }
      
      public function setLoopNum(num0:int) : void
      {
         this.loopNum = num0;
      }
   }
}

