package dataAll.equip.save
{
   import com.common.data.Base64;
   import com.common.text.TextWay;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.equip.EquipData;
   import dataAll.equip.creator.EquipEvoCtrl;
   import dataAll.equip.creator.EquipStrengthenCtrl;
   import dataAll.equip.creator.EquipUpgradeCtrl;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipFatherDefine;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.device.DeviceSave;
   import dataAll.equip.jewelry.JewelrySave;
   import dataAll.equip.shield.ShieldSave;
   import dataAll.equip.vehicle.VehicleSave;
   import dataAll.equip.weapon.WeaponSave;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.creator.ItemsUpgradeCtrl;
   import dataAll.items.save.ComplexSave;
   import dataAll.items.save.ItemsSave;
   
   public class EquipSave extends ComplexSave
   {
      
      public static var readNum:int = 0;
      
      public static var pro_arr:Array = [];
      
      protected var CF:NiuBiCF = new NiuBiCF();
      
      private var _obj:String = "";
      
      public var skillArr:Array = [];
      
      public var partType:String = "";
      
      public var imgName:String = "";
      
      public var proNum:int = 0;
      
      private var _heroSkillAddObj:String = "";
      
      public function EquipSave()
      {
         super();
         itemsType = ItemsDataGroup.TYPE_EQUIP;
         this.nowNum = 1;
         this.strengthenLv = 0;
         this.strengthenNum = 0;
         this.evoLv = 1;
      }
      
      public static function getSaveClass(type0:String) : Class
      {
         if(type0 == EquipType.VEHICLE)
         {
            return VehicleSave;
         }
         if(type0 == EquipType.DEVICE)
         {
            return DeviceSave;
         }
         if(type0 == EquipType.WEAPON)
         {
            return WeaponSave;
         }
         if(type0 == EquipType.JEWELRY)
         {
            return JewelrySave;
         }
         if(type0 == EquipType.FASHION)
         {
            return FashionSave;
         }
         if(type0 == EquipType.SHIELD)
         {
            return ShieldSave;
         }
         return EquipSave;
      }
      
      public function set nowNum(v0:Number) : void
      {
         this.CF.setAttribute("nowNum",v0);
      }
      
      public function get nowNum() : Number
      {
         return this.CF.getAttribute("nowNum");
      }
      
      public function set heroSkillAddObj(obj0:Object) : void
      {
         this._heroSkillAddObj = TextWay.toCode32(Base64.encodeObject(obj0));
      }
      
      public function get heroSkillAddObj() : Object
      {
         if(this._heroSkillAddObj != "")
         {
            return Base64.decodeObject(TextWay.getText32(this._heroSkillAddObj));
         }
         return {};
      }
      
      public function set obj(obj0:Object) : void
      {
         this._obj = Base64.encodeObject(obj0);
      }
      
      public function get obj() : Object
      {
         if(this._obj != "")
         {
            return Base64.decodeObject(this._obj);
         }
         return {};
      }
      
      public function get strengthenLv() : Number
      {
         return this.CF.getAttribute("strengthenLv");
      }
      
      public function set strengthenLv(v0:Number) : void
      {
         this.CF.setAttribute("strengthenLv",v0);
      }
      
      public function get strengthenNum() : Number
      {
         return this.CF.getAttribute("strengthenNum");
      }
      
      public function set strengthenNum(v0:Number) : void
      {
         this.CF.setAttribute("strengthenNum",v0);
      }
      
      public function get sMaxLv() : Number
      {
         return this.CF.getAttribute("sMaxLv");
      }
      
      public function set sMaxLv(v0:Number) : void
      {
         this.CF.setAttribute("sMaxLv",v0);
      }
      
      public function get evoLv() : Number
      {
         return this.CF.getAttribute("evoLv");
      }
      
      public function set evoLv(v0:Number) : void
      {
         this.CF.setAttribute("evoLv",v0);
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         super.inData_byObj(obj0);
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.obj = ClassProperty.copyObj(obj0["obj"]);
         if(obj0.hasOwnProperty("heroSkillAddObj"))
         {
            this.heroSkillAddObj = obj0["heroSkillAddObj"];
         }
         else
         {
            this.heroSkillAddObj = {};
         }
         this.fleshSMaxLv();
         itemsType = ItemsDataGroup.TYPE_EQUIP;
         var d0:EquipDefine = this.getDefine();
         if(d0.objInSaveB())
         {
            this.obj = d0.getAddObj();
         }
      }
      
      public function inDataByDefine(d0:EquipDefine) : void
      {
         this.setImgName(d0.name);
         this.partType = d0.type;
         cnName = d0.cnName;
         if(d0.objInSaveB())
         {
            this.obj = d0.getAddObj();
         }
         if(d0.skillArr.length > 0)
         {
            this.skillArr = d0.skillArr.concat([]);
         }
      }
      
      public function fleshSMaxLv() : void
      {
         if(isNaN(this.sMaxLv) || this.sMaxLv < this.strengthenLv)
         {
            this.sMaxLv = this.strengthenLv;
         }
      }
      
      public function purgoldInDefine(d0:EquipDefine) : void
      {
         name = d0.name;
         this.setImgName(d0.name);
         cnName = d0.cnName;
      }
      
      public function setImgName(str0:String) : void
      {
         this.imgName = str0;
      }
      
      public function getDataClass() : EquipData
      {
         return new EquipData();
      }
      
      override public function isImportantB() : Boolean
      {
         if(super.isImportantB())
         {
            return true;
         }
         if(EquipColor.moreColorPan(color,EquipColor.DARKGOLD))
         {
            return true;
         }
         if(EquipType.NORMAL_ARR.indexOf(this.partType) == -1)
         {
            return true;
         }
         return false;
      }
      
      override public function getTrueLevel() : int
      {
         return itemsLevel + addLevel;
      }
      
      public function getMaxLevel() : int
      {
         var add0:int = EquipUpgradeCtrl.maxAddLevel;
         return int(itemsLevel + add0);
      }
      
      override public function getStrengthenLv() : int
      {
         return this.strengthenLv;
      }
      
      override public function getEvoLv() : int
      {
         return this.evoLv;
      }
      
      override public function setStrengthenLvAndMax(lv0:int) : void
      {
         this.strengthenLv = lv0;
         this.sMaxLv = lv0;
      }
      
      public function getLevelTipString() : String
      {
         if(this.partType == EquipType.FASHION)
         {
            return "";
         }
         return this.getTrueLevel() + "";
      }
      
      public function getPartType() : String
      {
         return this.partType;
      }
      
      public function getDefine() : EquipDefine
      {
         return Gaming.defineGroup.equip.getDefine(this.imgName);
      }
      
      public function getFatherDefine() : EquipFatherDefine
      {
         var d0:EquipDefine = this.getDefine();
         return Gaming.defineGroup.equip.getFatherDefine(d0.father);
      }
      
      override public function copyOne() : ItemsSave
      {
         return this.clone();
      }
      
      public function clone() : EquipSave
      {
         var obj0:Object = ClassProperty.copyObj(this);
         var class0:Class = getSaveClass(this.partType);
         var s0:EquipSave = new class0();
         s0.inData_byObj(obj0);
         return s0;
      }
      
      override public function getChildType() : String
      {
         return this.partType;
      }
      
      override public function getChildTypeCnName() : String
      {
         return EquipType.getCnName(this.getChildType());
      }
      
      override public function isMoreRedB() : Boolean
      {
         return this.getDefine().isMoreRedB();
      }
      
      override public function getTrueName() : String
      {
         return this.imgName;
      }
      
      override public function getCnName() : String
      {
         return EquipEvoCtrl.getCnName(this.getDefine(),this.evoLv);
      }
      
      override public function getBaseCn() : String
      {
         return this.getDefine().cnName;
      }
      
      public function getComposeMustNum() : int
      {
         return this.getDefine().composeMustNum;
      }
      
      public function isHaveSkillAddB() : Boolean
      {
         return ComMethod.getObjElementNum(this.heroSkillAddObj) >= 1;
      }
      
      public function getTrueObj() : Object
      {
         var obj0:Object = this.obj;
         if(this.strengthenLv > 0)
         {
            obj0 = EquipStrengthenCtrl.getNewObj(this);
         }
         if(this.getDefine().isCanEvoB())
         {
            obj0 = EquipEvoCtrl.getNewObj(this.getDefine(),obj0,this.evoLv);
         }
         return obj0;
      }
      
      public function getSkillArr() : Array
      {
         return this.skillArr;
      }
      
      public function getStarNum() : int
      {
         return int(this.strengthenLv / 5);
      }
      
      override public function getAllUpgradeConverStoneNum() : int
      {
         return ItemsUpgradeCtrl.getAllConverStone(itemsLevel,addLevel,color,true);
      }
      
      public function getSortIdByOtherSave(s0:EquipSave) : String
      {
         var n:* = undefined;
         var str0:String = null;
         var v0:Number = NaN;
         var v1:Number = NaN;
         var mul0:Number = NaN;
         var moreNum0:int = 50;
         var moreMul0:Number = 0;
         for(n in this.obj)
         {
            v0 = Number(this.obj[n]);
            if(s0.obj.hasOwnProperty(n))
            {
               v1 = Number(s0.obj[n]);
               if(v0 > v1)
               {
                  moreNum0++;
                  if(v0 > 0 && v1 > 0)
                  {
                     mul0 = v0 / v1;
                     moreMul0 += mul0;
                  }
               }
               else if(v0 > v1)
               {
                  moreNum0--;
               }
            }
         }
         return moreNum0 + "_" + TextWay.toNum(String(Math.round(moreMul0 * 1000)),8);
      }
      
      override public function getSimulateData(pd0:NormalPlayerData) : IO_ItemsData
      {
         var da0:EquipData = this.getDataClass();
         da0.inData_bySave(this,pd0);
         return da0;
      }
   }
}

