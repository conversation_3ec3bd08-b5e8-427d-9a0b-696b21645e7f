package dataAll.arms.creator.evo
{
   import dataAll.arms.ArmsData;
   import dataAll.arms.creator.ArmsEvoCtrl;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.arms.define.ArmsType;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.must.define.MustDefine;
   
   public class BeforeArmsEvoCtrl
   {
      
      public static var darkgoldSkillArr:Array = ["fear_godArmsSkill","booby_godArmsSkill","fox_godArmsSkill","sickle_godArmsSkill","redFireSkill","addFlamer_ArmsSkill"];
      
      private static var christmasGun:ChristmasGunEvoCtrl = new ChristmasGunEvoCtrl();
      
      public function BeforeArmsEvoCtrl()
      {
         super();
      }
      
      private static function get darkgoldLv() : int
      {
         return 10;
      }
      
      private static function getMaxLv(armsD0:ArmsDefine) : int
      {
         var lv0:int = armsD0.evoMaxLv;
         var max0:int = 13;
         if(lv0 > max0)
         {
            lv0 = max0;
         }
         return lv0;
      }
      
      private static function canEvoB(armsD0:ArmsDefine, evoLv0:int) : Boolean
      {
         if(armsD0.isCanEvoB())
         {
            if(evoLv0 < getMaxLv(armsD0))
            {
               return true;
            }
         }
         return false;
      }
      
      private static function isMaxB(da0:ArmsData) : Boolean
      {
         return isMaxBBy(da0.def,da0.save.evoLv);
      }
      
      private static function isMaxBBy(armsD0:ArmsDefine, evoLv0:int) : Boolean
      {
         if(evoLv0 >= getMaxLv(armsD0))
         {
            return true;
         }
         return false;
      }
      
      private static function getChipMustNum(evoLv0:int) : int
      {
         var arr0:Array = [50,60,70,80,100,120,200,400,400];
         var index0:int = evoLv0 - 1;
         if(index0 < 0)
         {
            index0 = 0;
         }
         if(index0 > arr0.length - 1)
         {
            index0 = arr0.length - 1;
         }
         return arr0[index0];
      }
      
      private static function getAllChipMustNum(evoLv0:int) : int
      {
         var num0:Number = 0;
         for(var i:int = 1; i <= evoLv0; i++)
         {
            num0 += getChipMustNum(i);
         }
         return num0;
      }
      
      private static function getRadiumMustNum(evoLv0:int) : int
      {
         var arr0:Array = [0,120,140,160,200,240,320,600,600];
         var index0:int = evoLv0 - 1;
         if(index0 < 0)
         {
            index0 = 0;
         }
         if(index0 > arr0.length - 1)
         {
            index0 = arr0.length - 1;
         }
         return arr0[index0];
      }
      
      private static function getAllRadiumMustNum(evoLv0:int) : int
      {
         var num0:Number = 0;
         for(var i:int = 1; i <= evoLv0; i++)
         {
            num0 += getRadiumMustNum(i);
         }
         return num0;
      }
      
      private static function getTitaniumMustNum(evoLv0:int) : int
      {
         var arr0:Array = [0,180,210,240,300,360,480,720,720];
         var index0:int = evoLv0 - 1;
         if(index0 < 0)
         {
            index0 = 0;
         }
         if(index0 > arr0.length - 1)
         {
            index0 = arr0.length - 1;
         }
         return arr0[index0];
      }
      
      private static function getErosionMustNum(evoLv0:int) : int
      {
         var arr0:Array = [0,0,0,0,0,0,100,200];
         var index0:int = evoLv0 - 1;
         if(index0 < 0)
         {
            index0 = 0;
         }
         if(index0 > arr0.length - 1)
         {
            index0 = arr0.length - 1;
         }
         return arr0[index0];
      }
      
      private static function getMust(da0:ArmsData, diyB0:Boolean = true) : MustDefine
      {
         var diy0:DiyArmsEvoCtrl = null;
         var name0:String = da0.def.name;
         if(diyB0)
         {
            diy0 = ArmsEvoCtrl[name0];
            if(Boolean(diy0))
            {
               return diy0.getMust(da0);
            }
         }
         return getMustBy(da0.def,da0.save.evoLv);
      }
      
      public static function getMustBy(armsD0:ArmsDefine, evoLv0:int) : MustDefine
      {
         var mustLv0:int = evoLv0 + 1 + armsD0.evoMustFirstLv;
         if(mustLv0 >= darkgoldLv)
         {
            return getDarkgoldMustBy(armsD0,evoLv0);
         }
         var name0:String = armsD0.name;
         var d0:MustDefine = new MustDefine();
         d0.lv = 86;
         d0.coin = Gaming.defineGroup.normal.getLevelCoinIncome(d0.lv);
         var thingArr0:Array = [];
         var chip0:Number = getChipMustNum(mustLv0) * getChipMustMul(armsD0);
         var r0:Number = getRadiumMustNum(mustLv0);
         var t0:Number = getTitaniumMustNum(mustLv0);
         var e0:Number = getErosionMustNum(mustLv0);
         if(chip0 > 0)
         {
            thingArr0.push(armsD0.getChipName() + ";" + chip0);
         }
         if(r0 > 0)
         {
            thingArr0.push("armsRadium;" + r0);
         }
         if(t0 > 0)
         {
            thingArr0.push("armsTitanium;" + t0);
         }
         if(name0 == "rocketCate")
         {
            thingArr0.push("skeletonMedal_1;" + (mustLv0 >= 7 ? 60 : 30));
         }
         else
         {
            if(e0 > 0)
            {
               if(mustLv0 == 7)
               {
                  thingArr0.push("erosionFlamer;" + e0);
               }
               else if(mustLv0 == 8)
               {
                  thingArr0.push("AircraftGunCash;" + e0);
               }
            }
            if(mustLv0 == 9)
            {
               thingArr0.push("rocketCate;150");
            }
         }
         d0.inThingsDataByArr(thingArr0);
         return d0;
      }
      
      private static function getChipMustMul(armsD0:ArmsDefine) : Number
      {
         if(armsD0.name == "lightCone")
         {
            return 1.5;
         }
         return 1;
      }
      
      private static function getDarkgoldMustBy(armsD0:ArmsDefine, evoLv0:int) : MustDefine
      {
         var d0:MustDefine = null;
         var chip0:Number = NaN;
         var numMul0:Number = NaN;
         var typeArr0:Array = null;
         var gemNameArr0:Array = null;
         var gemName0:String = null;
         var mustLv0:int = evoLv0 + 1 + armsD0.evoMustFirstLv;
         var thingArr0:Array = [];
         var name0:String = armsD0.name;
         if(name0 == "rifleHornet")
         {
            thingArr0 = rifleHornetDarkgoldMust(mustLv0);
         }
         else if(name0 == "sniperCicada")
         {
            thingArr0 = sniperCicadaDarkgoldMust(mustLv0);
         }
         else if(name0 == "shotgunSkunk")
         {
            thingArr0 = shotgunSkunkDarkgoldMust(mustLv0);
         }
         else if(name0 == "pistolFox")
         {
            thingArr0 = pistolFoxDarkgoldMust(mustLv0);
         }
         else if(name0 == "redFire")
         {
            thingArr0 = redFireDarkgoldMust(mustLv0);
         }
         else if(name0 == "meltFlamer")
         {
            thingArr0 = meltFlamerDarkgoldMust(mustLv0);
         }
         else if(name0 == "lightCone")
         {
            thingArr0 = lightConeDarkgoldMust(mustLv0);
         }
         else
         {
            chip0 = 200;
            if(mustLv0 == 10)
            {
               chip0 = 300;
            }
            if(chip0 > 0)
            {
               thingArr0.push(armsD0.getChipName() + ";" + chip0);
            }
            numMul0 = 1.5;
            typeArr0 = [ArmsType.sniper];
            if(typeArr0.indexOf(armsD0.armsType) >= 0)
            {
               numMul0 = 1;
            }
            gemNameArr0 = getDarkgoldMustGemArr(armsD0);
            for each(gemName0 in gemNameArr0)
            {
               if(gemName0 == gemNameArr0[0])
               {
                  thingArr0.push(gemName0 + ";" + 200 * numMul0);
               }
               else
               {
                  thingArr0.push(gemName0 + ";" + 50 * numMul0);
               }
            }
         }
         d0 = new MustDefine();
         d0.lv = 90;
         d0.inThingsDataByArr(thingArr0);
         return d0;
      }
      
      private static function rifleHornetDarkgoldMust(mustLv0:int) : Array
      {
         var arr0:Array = ["poisonGem;9999999"];
         if(mustLv0 == 10)
         {
            arr0 = ["poisonGem;200","yearDog;100","yearSnake;100","yearHourse;60"];
         }
         if(mustLv0 == 11)
         {
            arr0 = ["rifleHornet;200","electricGem;100","poisonGem;200","frozenGem;100"];
         }
         if(mustLv0 == 12)
         {
            arr0 = ["rifleHornet;200","electricGem;100","poisonGem;200","yearPig;80"];
         }
         if(mustLv0 == 13)
         {
            arr0 = ["rifleHornet;200","electricGem;100","poisonGem;200","yearCattle;60"];
         }
         return arr0;
      }
      
      private static function sniperCicadaDarkgoldMust(mustLv0:int) : Array
      {
         var arr0:Array = ["poisonGem;9999999"];
         if(mustLv0 == 10 || mustLv0 == 11)
         {
            arr0 = ["sniperCicada;200","electricGem;200","fireGem;50","frozenGem;50"];
         }
         if(mustLv0 == 12)
         {
            arr0 = ["sniperCicada;200","electricGem;200","yearDog;100","yearMonkey;35"];
         }
         if(mustLv0 == 13)
         {
            arr0 = ["sniperCicada;200","electricGem;200","yearDog;100","yearMonkey;40"];
         }
         return arr0;
      }
      
      private static function shotgunSkunkDarkgoldMust(mustLv0:int) : Array
      {
         var arr0:Array = ["poisonGem;9999999"];
         if(mustLv0 == 10)
         {
            arr0 = ["shotgunSkunk;300","frozenGem;300","electricGem;75","fireGem;75"];
         }
         if(mustLv0 == 11)
         {
            arr0 = ["shotgunSkunk;200","frozenGem;300","electricGem;75","fireGem;75"];
         }
         if(mustLv0 == 12)
         {
            arr0 = ["shotgunSkunk;200","frozenGem;300","yearHourse;100","yearMonkey;75"];
         }
         return arr0;
      }
      
      private static function pistolFoxDarkgoldMust(mustLv0:int) : Array
      {
         var arr0:Array = ["poisonGem;9999999"];
         if(mustLv0 == 10)
         {
            arr0 = ["pistolFox;300","fireGem;300","poisonGem;75","frozenGem;75"];
         }
         if(mustLv0 == 11 || mustLv0 == 12)
         {
            arr0 = ["pistolFox;200","fireGem;300","poisonGem;100","yearHourse;49"];
         }
         if(mustLv0 == 13)
         {
            arr0 = ["pistolFox;200","fireGem;300","yearCattle;30","pianoGun;20"];
         }
         return arr0;
      }
      
      private static function redFireDarkgoldMust(mustLv0:int) : Array
      {
         var arr0:Array = ["poisonGem;9999999"];
         if(mustLv0 == 10)
         {
            arr0 = ["redFire;300","fireGem;200","electricGem;200","yearMouse;20"];
         }
         if(mustLv0 == 11 || mustLv0 == 12 || mustLv0 == 13)
         {
            arr0 = ["redFire;200","fireGem;200","yearSnake;50","yearMouse;20"];
         }
         return arr0;
      }
      
      private static function meltFlamerDarkgoldMust(mustLv0:int) : Array
      {
         var arr0:Array = ["poisonGem;9999999"];
         if(mustLv0 == 10)
         {
            arr0 = ["meltFlamer;300","poisonGem;200","frozenGem;200","yearRabbit;30"];
         }
         if(mustLv0 == 11)
         {
            arr0 = ["meltFlamer;200","poisonGem;200","yearSnake;50","yearRabbit;30"];
         }
         if(mustLv0 == 12)
         {
            arr0 = ["meltFlamer;200","poisonGem;200","yearCattle;30","pianoGun;20"];
         }
         return arr0;
      }
      
      private static function lightConeDarkgoldMust(mustLv0:int) : Array
      {
         var arr0:Array = ["poisonGem;9999999"];
         if(mustLv0 == 10)
         {
            arr0 = ["lightCone;300","electricGem;200","yearCattle;30","pianoGun;20"];
         }
         if(mustLv0 == 11)
         {
            arr0 = ["lightCone;200","electricGem;200","yearCattle;30","pianoGun;20"];
         }
         if(mustLv0 == 12)
         {
            arr0 = ["lightCone;200","electricGem;200","yearCattle;30","pianoGun;20"];
         }
         return arr0;
      }
      
      private static function getDarkgoldMustGemArr(armsD0:ArmsDefine) : Array
      {
         var type0:String = armsD0.armsType;
         if(type0 == ArmsType.sniper)
         {
            return ["electricGem","fireGem","frozenGem"];
         }
         if(type0 == ArmsType.shotgun)
         {
            return ["frozenGem","electricGem","fireGem"];
         }
         if(type0 == ArmsType.pistol)
         {
            return ["fireGem","frozenGem","poisonGem"];
         }
         return ["electricGem","fireGem","frozenGem"];
      }
      
      private static function traceAllMust() : void
      {
         var rd0:ArmsRangeDefine = null;
         var d0:ArmsDefine = null;
         var evoLv0:int = 0;
         var i:int = 0;
         var must0:MustDefine = null;
         var cnName0:String = null;
         var gift0:GiftAddDefineGroup = null;
         var str0:String = null;
         var darr0:Array = Gaming.defineGroup.bullet.blackArmsRangeArr;
         for each(rd0 in darr0)
         {
            d0 = rd0.def;
            if(d0.isCanEvoB())
            {
               trace("--------------------------------------------");
               evoLv0 = 1;
               for(i = 0; i < 99; i++)
               {
                  if(isMaxBBy(d0,evoLv0))
                  {
                     break;
                  }
                  must0 = getMustBy(d0,evoLv0);
                  cnName0 = ArmsEvoCtrl.getCnName(d0.cnName,evoLv0 + 1,d0);
                  gift0 = new GiftAddDefineGroup();
                  gift0.inMustDefineOnlyThings(must0);
                  str0 = cnName0 + "  " + gift0.getDescription(99,false);
                  trace(str0);
                  evoLv0++;
               }
            }
         }
      }
   }
}

