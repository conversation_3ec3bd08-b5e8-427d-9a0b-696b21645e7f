package dataAll.bullet.position
{
   import com.common.data.Line2;
   import dataAll.bullet.BulletDefine;
   
   public class BulletGatlinCount
   {
      
      public function BulletGatlinCount()
      {
         super();
      }
      
      public static function count(p0:BulletDefine, l0:Line2, attackNum0:int) : void
      {
         var gap0:Number = NaN;
         var pra0:Number = NaN;
         if(p0.gatlinNum > 0)
         {
            gap0 = Math.sin(attackNum0 % p0.gatlinNum / p0.gatlinNum * 2 * Math.PI) * p0.gatlinRange;
            pra0 = l0.ra + Math.PI / 2;
            l0.x += Math.cos(pra0) * gap0;
            l0.y += Math.sin(pra0) * gap0;
         }
      }
   }
}

