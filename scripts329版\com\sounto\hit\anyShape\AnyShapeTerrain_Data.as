package com.sounto.hit.anyShape
{
   import com.sounto.math.Maths;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.geom.Point;
   import gameAll.process.processFun.AnyShapeProcessFun;
   
   public class AnyShapeTerrain_Data
   {
      
      public var haveDataB:Boolean = false;
      
      public var width:int = 0;
      
      public var height:int = 0;
      
      public var bmpData:BitmapData;
      
      public var xp_arr:Array = [];
      
      public var yp_arr:Array = [];
      
      public var hitColor:uint = 16711680;
      
      public var density:int = 1;
      
      private var xp0:AnyShapePoint = new AnyShapePoint();
      
      private var yp0:AnyShapePoint = new AnyShapePoint();
      
      private var line_p:Point = new Point();
      
      private var loopH:int = 0;
      
      private var loopW:int = 0;
      
      public function AnyShapeTerrain_Data()
      {
         super();
      }
      
      public static function getVerticalRa(x0:Number, y0:Number, p0:AnyShapePoint, p1:AnyShapePoint) : Number
      {
         var x1:int = 0;
         var y1:int = 0;
         var up0:int = Math.abs(p1.before - y0);
         var down0:int = Math.abs(p1.affter - y0);
         var left0:int = Math.abs(p0.before - x0);
         var right0:int = Math.abs(p0.affter - x0);
         if(up0 < down0)
         {
            y1 = -up0;
         }
         else
         {
            y1 = down0;
         }
         if(left0 < right0)
         {
            x1 = -left0;
         }
         else
         {
            x1 = right0;
         }
         var ra0:Number = Math.atan2(Maths.Pn(y1) * Math.abs(x1),Maths.Pn(x1) * Math.abs(y1));
         if(!p0.hitB)
         {
            ra0 += Math.PI;
         }
         return ra0;
      }
      
      public function inData_bySp(sp0:DisplayObject, w0:int, h0:int, createPointB:Boolean = false) : void
      {
         this.bmpData = new BitmapData(w0,h0);
         this.bmpData.draw(sp0);
         this.width = w0;
         this.height = h0;
         if(createPointB)
         {
            this.createPoint_All();
         }
         this.haveDataB = true;
      }
      
      public function setFunProcessFun(fun0:AnyShapeProcessFun) : void
      {
         if(Boolean(this.bmpData))
         {
            this.loopW = this.bmpData.width / this.density + 1;
            this.loopH = this.bmpData.height / this.density + 1;
            fun0.anyshape = this;
            fun0.setLoopNum(this.loopH + this.loopW);
         }
      }
      
      public function haveIntersectionDataB() : Boolean
      {
         return this.xp_arr.length > 0 && this.yp_arr.length > 0;
      }
      
      public function clearData() : void
      {
         if(Boolean(this.bmpData))
         {
            this.bmpData.dispose();
            this.bmpData = null;
         }
         this.xp_arr.length = 0;
         this.yp_arr.length = 0;
         this.haveDataB = false;
      }
      
      private function createPoint_All() : void
      {
         var bmp0:BitmapData = this.bmpData;
         this.loopW = this.bmpData.width / this.density + 1;
         this.loopH = this.bmpData.height / this.density + 1;
         for(var m:int = 0; m < this.loopH + this.loopW; m++)
         {
            this.pre_createPoint_All(m);
         }
      }
      
      public function pre_createPoint_All(i:int) : void
      {
         if(i < this.loopH)
         {
            this.pre_createPoint_X(i);
         }
         else
         {
            this.pre_createPoint_Y(i - this.loopH);
         }
      }
      
      private function pre_createPoint_X(i:int) : void
      {
         var arr0:Array = null;
         var before_s:Boolean = false;
         var m:int = 0;
         var now_s:Boolean = false;
         var bmp0:BitmapData = this.bmpData;
         if(Boolean(bmp0))
         {
            arr0 = [];
            arr0.push(-100000);
            before_s = false;
            for(m = 0; m < this.loopW; m++)
            {
               now_s = bmp0.getPixel(m * this.density,i * this.density) == this.hitColor;
               if(now_s != before_s)
               {
                  arr0.push(m * this.density);
                  before_s = now_s;
               }
            }
            arr0.push(100000);
            this.xp_arr[i] = arr0;
         }
      }
      
      private function pre_createPoint_Y(i:int) : void
      {
         var arr0:Array = null;
         var before_s:Boolean = false;
         var m:int = 0;
         var now_s:Boolean = false;
         var bmp0:BitmapData = this.bmpData;
         if(Boolean(bmp0))
         {
            arr0 = [];
            arr0.push(-100000);
            before_s = false;
            for(m = 0; m < this.loopH; m++)
            {
               now_s = bmp0.getPixel(i * this.density,m * this.density) == this.hitColor;
               if(now_s != before_s)
               {
                  arr0.push(m * this.density);
                  before_s = now_s;
               }
            }
            arr0.push(100000);
            this.yp_arr[i] = arr0;
         }
      }
      
      public function hit(x0:int, y0:int) : Boolean
      {
         if(Boolean(this.bmpData))
         {
            return this.bmpData.getPixel(x0,y0) == this.hitColor;
         }
         return false;
      }
      
      public function hit_line(x0:Number, y0:Number, x1:Number, y1:Number, penetrationGap:int = 0) : Point
      {
         var len0:Number = NaN;
         var len:Number = NaN;
         var loopNum:int = 0;
         var cx:Number = NaN;
         var cy:Number = NaN;
         var nowPGap:Number = NaN;
         var penIndex:int = 0;
         var i:int = 0;
         var x3:int = 0;
         var y3:int = 0;
         if(this.bmpData.getPixel(x0,y0) == this.hitColor && penetrationGap == 0)
         {
            this.line_p.x = x0;
            this.line_p.y = y0;
            return this.line_p;
         }
         len0 = (x0 - x1) * (x0 - x1) + (y0 - y1) * (y0 - y1);
         len = Math.sqrt(len0);
         loopNum = len / 20 + 1;
         cx = (x1 - x0) / len * 20;
         cy = (y1 - y0) / len * 20;
         nowPGap = 0;
         penIndex = 0;
         for(i = 0; i < loopNum; i++)
         {
            x3 = i * cx + x0;
            y3 = i * cy + y0;
            if(this.bmpData.getPixel(x3,y3) == this.hitColor)
            {
               penIndex++;
               nowPGap = penIndex * 20;
               if(nowPGap >= penetrationGap)
               {
                  this.line_p.x = x3;
                  this.line_p.y = y3;
                  return this.line_p;
               }
            }
         }
         return null;
      }
      
      public function getYP(x0:int) : Array
      {
         return this.yp_arr[int(x0 / this.density)];
      }
      
      public function getLeftRight(x0:int, y0:int) : AnyShapePoint
      {
         var len0:int = 0;
         var right_x:int = 0;
         var left_x:int = 0;
         var index0:int = 0;
         var i:int = 0;
         var rx:int = 0;
         var arr0:Array = this.xp_arr[int(y0 / this.density)];
         if(Boolean(arr0))
         {
            len0 = int(arr0.length);
            right_x = -100;
            left_x = -100;
            index0 = -1;
            for(i = 0; i < len0; i++)
            {
               rx = int(arr0[i]);
               if(rx > x0)
               {
                  index0 = i;
                  right_x = rx;
                  break;
               }
            }
            left_x = int(arr0[index0 - 1]);
            this.xp0.before = left_x;
            this.xp0.affter = right_x;
            this.xp0.hitB = index0 % 2 == 0;
         }
         else
         {
            this.xp0.before = -100000;
            this.xp0.affter = 100000;
            this.xp0.hitB = false;
         }
         return this.xp0;
      }
      
      public function getUpDown(x0:int, y0:int) : AnyShapePoint
      {
         var len0:int = 0;
         var right_x:int = 0;
         var left_x:int = 0;
         var index0:int = 0;
         var i:int = 0;
         var rx:int = 0;
         var arr0:Array = this.yp_arr[int(x0 / this.density)];
         if(Boolean(arr0))
         {
            len0 = int(arr0.length);
            right_x = -100;
            left_x = -100;
            index0 = -1;
            for(i = 0; i < len0; i++)
            {
               rx = int(arr0[i]);
               if(rx > y0)
               {
                  index0 = i;
                  right_x = rx;
                  break;
               }
            }
            left_x = int(arr0[index0 - 1]);
            this.yp0.before = left_x;
            this.yp0.affter = right_x;
            this.yp0.hitB = index0 % 2 == 0;
         }
         else
         {
            this.yp0.before = -100000;
            this.yp0.affter = 100000;
            this.yp0.hitB = false;
         }
         return this.yp0;
      }
      
      public function setNote(n0:AnyShapeNode) : void
      {
         var p0:AnyShapePoint = this.getLeftRight(n0.x,n0.y);
         var p1:AnyShapePoint = this.getUpDown(n0.x,n0.y);
         n0.left = p0.before;
         n0.right = p0.affter;
         n0.up = p1.before;
         n0.down = p1.affter;
         n0.hitB = p0.hitB;
         n0.maxX = n0.right;
         n0.minX = n0.left;
         n0.maxY = n0.down;
         n0.minY = n0.up;
         if(n0.hitB)
         {
            n0.maxY = n0.up;
            n0.minY = n0.down;
            n0.maxX = n0.left;
            n0.minX = n0.right;
         }
      }
      
      public function getSlopeVerticalRa(x0:Number, y0:Number) : Number
      {
         var p0:AnyShapePoint = this.getLeftRight(x0,y0);
         var p1:AnyShapePoint = this.getUpDown(x0,y0);
         return getVerticalRa(x0,y0,p0,p1);
      }
      
      public function getMinY(x0:Number, y0:Number) : Number
      {
         var s0:AnyShapePoint = this.getUpDown(x0,y0);
         if(s0.hitB)
         {
            return s0.before;
         }
         return s0.affter;
      }
   }
}

