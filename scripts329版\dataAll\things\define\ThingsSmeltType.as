package dataAll.things.define
{
   public class ThingsSmeltType
   {
      
      public static const coin:String = "coin";
      
      public static const gold:String = "gold";
      
      public static const stone:String = "stone";
      
      public static const effect:String = "effect";
      
      public static const currency:String = "currency";
      
      public static const box:String = "box";
      
      public static const armsChip:String = "armsChip";
      
      public static const equipChip:String = "equipChip";
      
      public static const armsEquip:String = "armsEquip";
      
      public static const pet:String = "pet";
      
      public static const vehicle:String = "vehicle";
      
      public static const otherChip:String = "otherChip";
      
      public static const festival:String = "festival";
      
      public static const other:String = "other";
      
      public static const no:String = "";
      
      public static const arr:Array = [armsEquip,armsChip,equipChip,otherChip,vehicle,box,stone,pet,effect,festival,currency,coin,gold,other,no];
      
      public function ThingsSmeltType()
      {
         super();
      }
   }
}

