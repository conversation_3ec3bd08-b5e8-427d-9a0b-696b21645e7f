package dataAll.ui.guide
{
   import com.sounto.cf.CodeCF;
   import com.sounto.cf.StringCF;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.login.LoginData4399;
   
   public class GuideSave
   {
      
      public static var pro_arr:Array = [];
      
      private var stringCF:StringCF = new StringCF();
      
      public var first:Boolean = false;
      
      public var arms:Boolean = false;
      
      public var equip:Boolean = false;
      
      public var more:Boolean = false;
      
      public var skill:Boolean = false;
      
      public var task:Boolean = false;
      
      public var mainTask:Boolean = false;
      
      public var endless:Boolean = false;
      
      public var partsUnlock:Boolean = false;
      
      public var blackMarketUnlock:Boolean = false;
      
      public var petUnlock:Boolean = false;
      
      public var arenaInfo:Boolean = false;
      
      public var arenaSeasonInfo:Boolean = false;
      
      public var kingTaskInfo:Boolean = false;
      
      public var extraTaskInfo:Boolean = false;
      
      public var arenaTestB:Boolean = false;
      
      public var firstSaveB:Boolean = false;
      
      public var houseInfo:Boolean = false;
      
      public var endlessInfo:Boolean = false;
      
      public var numlockKeyTip1:Boolean = false;
      
      public var numlockKeyTip2:Boolean = false;
      
      public var firstDoubleB:Boolean = false;
      
      public function GuideSave()
      {
         super();
         this.uu = "";
         this.uu2 = "";
         this.ui = "";
         this.un = "";
         this.un2 = "";
      }
      
      public static function dealObj(new0:String, obj0:Object) : void
      {
         var a0:String = "ui";
         if(!obj0.hasOwnProperty("guide"))
         {
            obj0["guide"] = {};
         }
         if(!obj0.guide.hasOwnProperty(a0))
         {
            obj0.guide[a0] = new0;
         }
      }
      
      public static function dealU(u0:String, u2:String, obj0:Object) : void
      {
         if(!obj0.hasOwnProperty("guide"))
         {
            obj0["guide"] = {};
         }
         if(!obj0.guide.hasOwnProperty("uu") || !obj0.guide.hasOwnProperty("uu2"))
         {
            obj0.guide.uu = u0;
            obj0.guide.uu2 = u2;
         }
      }
      
      public function get ui() : String
      {
         return this.stringCF.getAttribute("ui") as String;
      }
      
      public function set ui(str0:String) : void
      {
         this.stringCF.setAttribute("ui",str0);
      }
      
      public function get uu() : String
      {
         return this.stringCF.getAttribute("uu") as String;
      }
      
      public function set uu(str0:String) : void
      {
         this.stringCF.setAttribute("uu",str0);
      }
      
      public function get uu2() : String
      {
         return this.stringCF.getAttribute("uu2") as String;
      }
      
      public function set uu2(str0:String) : void
      {
         this.stringCF.setAttribute("uu2",str0);
      }
      
      public function get un() : String
      {
         return this.stringCF.getAttribute("un") as String;
      }
      
      public function set un(str0:String) : void
      {
         this.stringCF.setAttribute("un",str0);
      }
      
      public function get un2() : String
      {
         return this.stringCF.getAttribute("un2") as String;
      }
      
      public function set un2(str0:String) : void
      {
         this.stringCF.setAttribute("un2",str0);
      }
      
      public function dealObj2(new0:String) : void
      {
         var a0:String = "ui";
         this[a0] = new0;
      }
      
      public function getObj2() : Object
      {
         var a0:String = "ui";
         return this[a0];
      }
      
      public function dealUN(apiLogObj0:LoginData4399 = null) : void
      {
         var uid0:String = null;
         if(this.un == "")
         {
            if(Boolean(apiLogObj0))
            {
               uid0 = apiLogObj0.uid;
               if(uid0 != "")
               {
                  this.un = CodeCF.uidToCode(uid0);
                  this.un2 = uid0;
               }
            }
         }
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function overOne(name0:String) : void
      {
         if(this.hasOwnProperty(name0))
         {
            this[name0] = true;
         }
      }
      
      public function setOne(name0:String, bb0:Boolean) : void
      {
         if(this.hasOwnProperty(name0))
         {
            this[name0] = bb0;
         }
      }
      
      public function getOne(name0:String) : Boolean
      {
         if(this.hasOwnProperty(name0))
         {
            return this[name0];
         }
         return false;
      }
      
      public function getShowNumTip() : Boolean
      {
         return !(this.numlockKeyTip1 && this.numlockKeyTip2);
      }
   }
}

