package com.sounto.cf
{
   import flash.utils.getTimer;
   
   public class SountoLocal64 extends Sounto64
   {
      
      private static var sountoArr:Vector.<SountoLocal64> = new Vector.<SountoLocal64>();
      
      private static var tempUseString:String = "";
      
      private var keyObj:Object = null;
      
      private var codeObj:Object = null;
      
      public function SountoLocal64()
      {
         super();
      }
      
      public static function staticInit() : void
      {
         var s0:SountoLocal64 = null;
         var tt0:Number = getTimer();
         for(var i:int = 0; i < 100; i++)
         {
            s0 = new SountoLocal64();
            s0.create();
            sountoArr.push(s0);
         }
      }
      
      public static function getRandom() : SountoLocal64
      {
         var s0:SountoLocal64 = sountoArr[int(Math.random() * sountoArr.length)];
         var new0:SountoLocal64 = new SountoLocal64();
         new0.keyObj = s0.keyObj;
         new0.codeObj = s0.codeObj;
         return new0;
      }
      
      private function create() : void
      {
         var s0:String = null;
         this.keyObj = {};
         this.codeObj = {};
         tempUseString = useString;
         var len0:int = strArrayString.length;
         var keynum0:int = useString.length / strArrayString.length;
         for(var i:int = 0; i < len0; i++)
         {
            s0 = strArrayString.charAt(i);
            this.codeObj[s0] = [];
            this.randomMore(s0,keynum0);
         }
      }
      
      private function randomMore(str0:String, num0:int) : void
      {
         var s2:String = null;
         var num2:int = 0;
         while(num2 < num0)
         {
            s2 = this.randomOne(str0);
            if(this.keyObj[s2] == null)
            {
               num2++;
               this.keyObj[s2] = str0;
               this.codeObj[str0].push(s2);
            }
         }
      }
      
      private function randomOne(str0:String) : String
      {
         var index0:int = 0;
         var s2:String = null;
         var len0:int = tempUseString.length;
         var s0:String = "";
         for(var i:int = 0; i < 1; i++)
         {
            index0 = int(Math.random() * len0);
            s2 = tempUseString.charAt(index0);
            s0 += s2;
            tempUseString = tempUseString.replace(s2,"");
         }
         return s0;
      }
      
      public function encode(str0:String) : String
      {
         var arr0:Array = null;
         var c0:String = null;
         var xx0:int = 0;
         if(!str0)
         {
            str0 = "0";
         }
         if(str0 == "")
         {
            str0 = "0";
         }
         var num0:Number = Number(str0);
         if(isNaN(num0))
         {
            str0 = "0";
         }
         var len0:int = str0.length;
         var s0:String = "";
         for(var i:int = len0 - 1; i >= 0; i--)
         {
            arr0 = this.codeObj[str0.charAt(i)];
            if(Boolean(arr0))
            {
               c0 = arr0[int(Math.random() * arr0.length)];
               s0 += c0;
            }
            else
            {
               xx0 = 0;
            }
         }
         return s0;
      }
      
      public function decode(str0:String) : String
      {
         var s2:String = null;
         var s3:String = null;
         if(!str0)
         {
            return "0";
         }
         if(str0 == "")
         {
            return "0";
         }
         var len0:int = str0.length;
         var s0:String = "";
         for(var i:int = len0 - 1; i >= 0; i--)
         {
            s2 = str0.charAt(i);
            s3 = this.keyObj[s2];
            s0 += s3;
         }
         return s0;
      }
   }
}

