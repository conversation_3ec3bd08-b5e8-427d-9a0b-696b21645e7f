package gameAll.body.key
{
   import com.sounto.key.Keys;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.image.CarImage;
   import gameAll.bullet.CarBulletLauncher;
   
   public class CarBody<PERSON>ey extends VehicleBodyKey
   {
      
      private var carImg:CarImage;
      
      private var carBulletLauncher:CarBulletLauncher;
      
      public function CarBodyKey(_BB:IO_NormalBody)
      {
         super(_BB);
         this.carImg = _BB.getImg() as CarImage;
         this.carBulletLauncher = _BB.getBullet() as CarBulletLauncher;
      }
      
      override public function setShootPoint(x0:int, y0:int, tweenMul0:Number = 0) : void
      {
         super.setShootPoint(x0,y0);
         this.carBulletLauncher.setShootPoint(x0,y0,tweenMul0);
      }
      
      override protected function shootDown() : void
      {
         this.carBulletLauncher.mouseDown();
      }
      
      override protected function shootUp() : void
      {
         this.carBulletLauncher.mouseUp();
      }
      
      override public function keyPan(arr0:Array) : void
      {
         if(ai.enabled)
         {
            return;
         }
         super.keyPan(arr0);
         if(rideShootB == false)
         {
            this.armsPan(arr0);
         }
      }
      
      public function armsPan(arr0:Array) : void
      {
         var code0:int = 0;
         var kQ:Keys = null;
         var nextArmsKeyArr0:Array = keySave.getCodeArr("nextArms");
         for each(code0 in nextArmsKeyArr0)
         {
            kQ = arr0[code0];
            if(kQ.s == "down")
            {
               this.carBulletLauncher.nextArms();
               break;
            }
         }
      }
   }
}

