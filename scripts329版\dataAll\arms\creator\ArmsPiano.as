package dataAll.arms.creator
{
   import UI.base.alert.AlertBox;
   import dataAll.arms.ArmsData;
   
   public class ArmsPiano
   {
      
      public static const STR_ARR:Array = ["1","2","3","4","5","6","7"];
      
      private static const FIRST_ARR:Array = ["-","+"];
      
      private static var now:ArmsData = null;
      
      public function ArmsPiano()
      {
         super();
      }
      
      private static function get alert() : AlertBox
      {
         return Gaming.uiGroup.alertBox;
      }
      
      public static function get MIN_LEN() : int
      {
         return 7;
      }
      
      public static function editData(da0:ArmsData) : void
      {
         now = da0;
         var str0:String = da0.save.o as String;
         alert.textInput.showTextInput("编辑乐谱，具体格式请前往图鉴>百科>问题大全>乐谱编辑中查看。",str0,yesEditFun,"yesAndNo",da0.getPianoMaxLen() * 2);
      }
      
      private static function yesEditFun(str0:String) : void
      {
         var da0:ArmsData = now;
         var arr0:Array = strToArr(str0);
         var maxLen0:int = da0.getPianoMaxLen();
         if(arr0.length > maxLen0)
         {
            arr0.length = maxLen0;
            alert.showInfo("乐谱长度超过上限" + maxLen0 + "，系统将为其自动裁剪。");
         }
         else if(arr0.length < MIN_LEN)
         {
            alert.showError("乐谱长度至少要有" + MIN_LEN + "，才能对敌人造成音爆伤害。");
         }
         var newStr0:String = arrToStr(arr0);
         da0.save.o = newStr0;
         da0.pianoArr = arr0;
         da0.pianoIndex = 0;
      }
      
      public static function strToArr(str0:String) : Array
      {
         var s0:String = null;
         var first0:String = "";
         var len0:int = str0.length;
         var sarr0:Array = [];
         for(var i:int = 0; i < len0; i++)
         {
            s0 = str0.charAt(i);
            if(STR_ARR.indexOf(s0) >= 0)
            {
               sarr0.push(first0 + s0);
               first0 = "";
            }
            else if(s0 == "+")
            {
               first0 = "$";
            }
            else if(s0 == "-")
            {
               first0 = "_";
            }
         }
         return sarr0;
      }
      
      public static function arrToStr(arr0:Array) : String
      {
         var s0:String = null;
         var str0:String = "";
         for each(s0 in arr0)
         {
            if(s0.length > 1)
            {
               s0 = s0.replace("$","+");
               s0 = s0.replace("_","-");
            }
            str0 += s0;
         }
         return str0;
      }
      
      public static function getSoundUrl(s0:String) : String
      {
         return "uiSound/p" + s0;
      }
   }
}

