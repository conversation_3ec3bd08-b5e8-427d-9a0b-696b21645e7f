package dataAll.pet.gene.define
{
   import com.sounto.oldUtils.ComMethod;
   
   public class GeneDropDefine
   {
      
      public var name:Array = [];
      
      public var normalPro:Array = [];
      
      public var bossPro:Array = [];
      
      public var proNum:Array = [];
      
      public var talentSkillNum:Array = [];
      
      public var laterSkillNum:Array = [];
      
      public var valueRange:Array = [];
      
      public function GeneDropDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var pro0:String = null;
         var pro_arr0:Array = ["bossPro","normalPro","proNum","talentSkillNum","laterSkillNum"];
         for(n in pro_arr0)
         {
            pro0 = pro_arr0[n];
            this[pro0] = ComMethod.stringToNumberArr(String(xml0[pro0]));
            if(this[pro0].length < this.name.length)
            {
               INIT.showError(pro0 + "的长度" + this[pro0].length + "<" + this.name.length);
            }
         }
         this.name = ComMethod.stringToStringArr(String(xml0.name));
         this.valueRange = ComMethod.stringToRangeArr(String(xml0.valueRange));
      }
      
      public function getDropColorByGeneType(type0:String, rareDropAdd0:Number) : String
      {
         var mul0:Number = NaN;
         var baseArr0:Array = this[type0 + "Pro"];
         var arr0:Array = baseArr0;
         if(rareDropAdd0 > 0)
         {
            mul0 = (rareDropAdd0 + 1) * 2;
            arr0 = baseArr0.concat();
            arr0[3] *= mul0;
            arr0[4] *= mul0;
         }
         var index0:int = ComMethod.getPro_byArrSum(arr0);
         return this.name[index0];
      }
   }
}

