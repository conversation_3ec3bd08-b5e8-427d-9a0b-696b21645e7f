package dataAll.arms.creator.evo
{
   import dataAll.arms.ArmsData;
   import dataAll.arms.creator.ArmsEvoCtrl;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.save.ArmsSave;
   import dataAll.must.define.MustDefine;
   
   public class DiyArmsEvoCtrl
   {
      
      public function DiyArmsEvoCtrl()
      {
         super();
      }
      
      public function getCnName(cn0:String, evoLv0:int, d0:ArmsDefine) : String
      {
         return ArmsEvoCtrl.getCnName(cn0,evoLv0,d0,false);
      }
      
      public function getHurtMul(evoLv0:int, d0:ArmsDefine) : Number
      {
         return ArmsEvoCtrl.getHurtMul(evoLv0,d0,false);
      }
      
      public function doEvo(da0:ArmsData) : void
      {
         var s0:ArmsSave = da0.save;
         s0.doEvo();
      }
      
      public function getMust(da0:ArmsData) : MustDefine
      {
         return ArmsEvoCtrl.getMust(da0,false);
      }
      
      public function getArmsImageName(d0:ArmsDefine, evoLv0:int) : String
      {
         return ArmsEvoCtrl.getArmsImageNameOne(d0,evoLv0);
      }
   }
}

