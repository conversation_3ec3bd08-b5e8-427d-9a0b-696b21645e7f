package dataAll.pro
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.NumberMethod;
   
   public class PropertyArrayDefine
   {
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var first:String = "";
      
      public var unit:String = "";
      
      public var fixedNum:int = 0;
      
      public var blackRange:Array = [];
      
      public var bigBestB:Boolean = true;
      
      public var gatherColor:String = "gray";
      
      public var wanB:Boolean = false;
      
      public var dataArr:Array;
      
      public var typeArr:Array = null;
      
      public function PropertyArrayDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         this.name = String(xml0.@name);
         this.cnName = String(xml0.@cnName);
         this.unit = String(xml0.@unit);
         this.first = String(xml0.@first);
         if(String(xml0.@gatherColor) != "")
         {
            this.gatherColor = String(xml0.@gatherColor);
         }
         if(String(xml0.@fixedNum) != "")
         {
            this.fixedNum = int(xml0.@fixedNum);
         }
         if(String(xml0.@bigBestB) != "")
         {
            this.bigBestB = Boolean(int(xml0.@bigBestB));
         }
         if(String(xml0.@wanB) != "")
         {
            this.wanB = Boolean(int(xml0.@wanB));
         }
         if(String(xml0.@blackRange) != "")
         {
            this.blackRange = ComMethod.stringToNumberArr(xml0.@blackRange,"~");
         }
         if(String(xml0.@typeArr) != "")
         {
            this.typeArr = ComMethod.stringToStringArr(xml0.@typeArr);
         }
         if(this.blackRange.length == 0)
         {
            this.blackRange = [-0.02,0.02];
         }
         this.addDataArray(xml0);
      }
      
      protected function addDataArray(str0:String) : void
      {
         var arr0:Array = null;
         var n:* = undefined;
         var str1:String = null;
         if(!str0)
         {
            return;
         }
         var baifenB:Boolean = str0.indexOf("%") >= 0;
         if(baifenB)
         {
            arr0 = str0.split("%");
         }
         else
         {
            arr0 = str0.split("\r\n");
         }
         var arr2:Array = [];
         for(n in arr0)
         {
            str1 = arr0[n];
            str1 = TextWay.toHan2(str1);
            if(Boolean(str1) && str1 != "")
            {
               if(baifenB)
               {
                  arr2[n] = Number(Number(Number(str1) / 100).toFixed(this.fixedNum + 2));
               }
               else
               {
                  arr2[n] = Number(Number(str1).toFixed(this.fixedNum));
               }
            }
         }
         this.dataArr = arr2;
      }
      
      protected function addDataArrayTest(str0:String) : void
      {
         var arr0:Array = null;
         var n:* = undefined;
         var bb0:Boolean = false;
         var str1:String = null;
         var xx:int = 0;
         if(!str0)
         {
            return;
         }
         str0 = TextWay.toHan2(str0);
         var baifenB:Boolean = str0.indexOf("%") >= 0;
         if(baifenB)
         {
            arr0 = str0.split("%");
         }
         else
         {
            arr0 = str0.split(" ");
         }
         var arr2:Array = [];
         for(n in arr0)
         {
            str1 = arr0[n];
            if(Boolean(str1) && str1 != "")
            {
               if(baifenB)
               {
                  arr2[n] = Number(Number(Number(str1) / 100).toFixed(this.fixedNum + 2));
               }
               else
               {
                  arr2[n] = Number(Number(str1).toFixed(this.fixedNum));
               }
            }
         }
         bb0 = ArrayMethod.elementSamePan(arr2,this.dataArr);
         if(!bb0)
         {
            xx = 0;
         }
      }
      
      public function getDataLen() : int
      {
         return this.dataArr.length;
      }
      
      public function getValue(lv0:int) : Number
      {
         if(!this.dataArr)
         {
            return 0;
         }
         var arr0:Array = this.dataArr;
         var index0:int = lv0 - 1;
         if(index0 < 0)
         {
            index0 = 0;
         }
         if(index0 > arr0.length - 1)
         {
            index0 = arr0.length - 1;
         }
         return arr0[index0];
      }
      
      public function getSum(lv0:int) : Number
      {
         var sum0:Number = 0;
         for(var i:int = 1; i <= lv0; i++)
         {
            sum0 += this.getValue(i);
         }
         return sum0;
      }
      
      public function findLvBySum(nowSum0:Number) : int
      {
         var sum0:Number = 0;
         var len0:int = int(this.dataArr.length);
         for(var i:int = 1; i < len0; i++)
         {
            sum0 += this.getValue(i);
            if(nowSum0 < sum0)
            {
               return i - 1;
            }
         }
         return len0;
      }
      
      public function getBlackValueRange(v0:Number) : Array
      {
         var range0:Array = this.blackRange;
         var arr0:Array = [v0,v0];
         if(range0.length > 0)
         {
            if(range0[1] >= 1)
            {
               arr0 = [v0 * range0[0],v0 * range0[1]];
            }
            else
            {
               arr0 = [v0 + range0[0],v0 + range0[1]];
            }
         }
         arr0[0] = this.fixedNumber(arr0[0]);
         arr0[1] = this.fixedNumber(arr0[1]);
         return arr0;
      }
      
      public function fixedNumber(v0:Number) : Number
      {
         if(this.unit == "%")
         {
            return Number(Number(v0).toFixed(this.fixedNum + 2));
         }
         return Number(Number(v0).toFixed(this.fixedNum));
      }
      
      public function isPer() : Boolean
      {
         return this.unit == "%";
      }
      
      public function getValueString(v0:Number, firstB0:Boolean = true, unit0:String = null) : String
      {
         if(unit0 == null)
         {
            unit0 = this.unit;
         }
         var str0:String = "";
         if(this.isPer())
         {
            str0 = Math.round(v0 * 100) + "";
         }
         else if(this.wanB)
         {
            str0 = NumberMethod.toFixedWan(v0);
         }
         else
         {
            str0 = v0 + "";
         }
         return (firstB0 ? this.getFirst(v0) : "") + str0 + unit0;
      }
      
      public function getFixedValueString(v0:*) : String
      {
         if(v0 is String)
         {
            return v0;
         }
         v0 = this.fixedNumber(v0);
         return this.getValueString(v0);
      }
      
      public function getNoUnitValueString(v0:Number) : String
      {
         var first0:String = this.getFirst(v0);
         if(this.isPer())
         {
            return first0 + Math.round(v0 * 100) + this.unit;
         }
         return first0 + String(v0);
      }
      
      public function getFirst(v0:*) : String
      {
         if(this.first != "")
         {
            if(v0 is Number)
            {
               if(this.first == "+" && Number(v0) < 0)
               {
                  return "";
               }
            }
         }
         return this.first;
      }
   }
}

