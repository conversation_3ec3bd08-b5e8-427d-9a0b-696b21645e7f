package gameAll.body.skill
{
   import gameAll.body.IO_NormalBody;
   import gameAll.body.data.NormalBodyDieCtrl;
   
   public class StateExtraData
   {
      
      public var x:Number = 0;
      
      public var y:Number = 0;
      
      public var value:Number = 0;
      
      public var scaleX:Number = 1;
      
      public var index:int = 0;
      
      public var num:int = 0;
      
      public var secNum:int = 0;
      
      public var obj:Object = {};
      
      public var arr:Array = [];
      
      public var visible:Boolean = true;
      
      public function StateExtraData()
      {
         super();
      }
      
      public function addSummonedArr(arr0:Array, x0:Number = NaN, y0:Number = NaN, firstLabel0:String = "") : void
      {
         var b0:IO_NormalBody = null;
         for each(b0 in arr0)
         {
            this.addSummoned(b0);
            if(!isNaN(x0) && !isNaN(y0))
            {
               b0.setXY(x0,y0);
            }
            if(firstLabel0 != "")
            {
               b0.getImg().setOrder([firstLabel0,"stand"]);
            }
         }
      }
      
      public function addSummoned(b0:IO_NormalBody) : void
      {
         this.arr.push(b0);
      }
      
      public function summonedTimer() : void
      {
         var b0:IO_NormalBody = null;
         var newArr0:Array = [];
         for each(b0 in this.arr)
         {
            if(b0.getDieCtrl().dieState != NormalBodyDieCtrl.DEL)
            {
               newArr0.push(b0);
            }
         }
         this.arr = newArr0;
      }
   }
}

