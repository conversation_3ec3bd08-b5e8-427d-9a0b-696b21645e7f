package dataAll.items.house
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.StringDate;
   import dataAll.items.IO_ItemsData;
   
   public class HouseDataCtrl
   {
      
      private static var dayMust:String = TextWay.toCode32("2000");
      
      private static var maxDayNum:String = TextWay.toCode32("10");
      
      public function HouseDataCtrl()
      {
         super();
      }
      
      public static function countCoin(da0:IO_ItemsData, nowDate0:StringDate) : int
      {
         var timeDate0:StringDate = null;
         var dayMust0:int = int(TextWay.getText32(dayMust));
         var maxDayNum0:int = int(TextWay.getText32(maxDayNum));
         var time0:String = da0.getSave().getInHouseTime();
         var cDay0:int = -1;
         if(time0 != "")
         {
            timeDate0 = new StringDate();
            timeDate0.inData_byStr(time0);
            cDay0 = nowDate0.reductionOne(timeDate0);
         }
         if(cDay0 < 0 || cDay0 > maxDayNum0)
         {
            cDay0 = maxDayNum0;
         }
         return cDay0 * dayMust0;
      }
      
      public static function countDay(da0:IO_ItemsData, nowDate0:StringDate) : int
      {
         var timeDate0:StringDate = null;
         var time0:String = da0.getSave().getInHouseTime();
         var cDay0:int = 0;
         if(time0 != "")
         {
            timeDate0 = new StringDate();
            timeDate0.inData_byStr(time0);
            cDay0 = nowDate0.reductionOne(timeDate0);
         }
         return cDay0;
      }
   }
}

