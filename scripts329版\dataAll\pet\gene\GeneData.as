package dataAll.pet.gene
{
   import com.common.text.TextWay;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.equip.EquipPropertyData;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.NormalItemsData;
   import dataAll.items.save.ItemsSave;
   import dataAll.pet.gene.creator.GeneDataGrowthCtrl;
   import dataAll.pet.gene.save.GeneSave;
   
   public class GeneData extends NormalItemsData implements IO_ItemsData
   {
      
      public var save:GeneSave;
      
      public function GeneData()
      {
         super();
      }
      
      public function inData_bySave(s0:GeneSave, pd0:NormalPlayerData) : void
      {
         this.save = s0;
         setPlayerData(pd0);
      }
      
      override public function toOneSortId(str0:String) : void
      {
         var index0:int = 0;
         tempSortId = "";
         if(str0 == "type")
         {
            index0 = this.save.getDefine().index;
            tempSortId = TextWay.toNum(index0 + "",3) + "_" + this.save.id;
         }
      }
      
      public function normalClone() : IO_ItemsData
      {
         return null;
      }
      
      public function getPetHead() : String
      {
         return GeneDataGrowthCtrl.getPetHead(this);
      }
      
      public function getGrowAllPro() : Number
      {
         return GeneDataGrowthCtrl.getAllProValue(this.save.growObj);
      }
      
      override public function getSave() : ItemsSave
      {
         return this.save;
      }
      
      public function getCnType() : String
      {
         return "基因体";
      }
      
      public function getIconImgUrl(maxWidth0:int = 0, maxHeight0:int = 0) : String
      {
         return this.save.getDefine().iconUrl;
      }
      
      public function getCnName() : String
      {
         return this.save.cnName;
      }
      
      public function getSellPrice() : Number
      {
         return this.save.getSellPrice("things");
      }
      
      public function getWearLevel() : int
      {
         return this.save.getTrueLevel();
      }
      
      public function getTypeId() : String
      {
         return "05";
      }
      
      public function getChildTypeId() : String
      {
         return "01";
      }
      
      public function getBodyName() : String
      {
         return this.save.getDefine().bodyName;
      }
      
      public function getAddData() : EquipPropertyData
      {
         var da0:EquipPropertyData = new EquipPropertyData();
         da0.inData_byObj(this.save.obj);
         return da0;
      }
      
      public function getGatherTip() : String
      {
         var str0:String = "";
         return str0 + Gaming.defineGroup.geneCreator.getGather(this.save);
      }
   }
}

