package dataAll.equip.define
{
   public class EquipPro
   {
      
      public static const dayLoveAdd:String = "dayLoveAdd";
      
      public static const sweepingNum:String = "sweepingNum";
      
      public static const demStroneDropNum:String = "demStroneDropNum";
      
      public static const demBallDropNum:String = "demBallDropNum";
      
      public static var dropNameArr:Array = ["coinMul","petBookDropPro","rareGeneDropPro","specialPartsDropPro","gemDropPro","blackArmsDropPro","blackEquipDropPro","rareArmsDropPro","rareEquipDropPro","weaponDropPro","deviceDropPro","bloodStoneDropPro","arenaStampDropNum","vehicleCashDropNum","lifeCatalystDropPro","godStoneDropPro","converStoneDropPro","taxStampDropPro","loveAdd",dayLoveAdd,sweepingNum,demStroneDropNum,demBallDropNum];
      
      public function EquipPro()
      {
         super();
      }
      
      public static function getSumMax(name0:String) : Number
      {
         if(name0 == sweepingNum)
         {
            return 3 * 3;
         }
         if(name0 == demStroneDropNum)
         {
            return 2;
         }
         if(name0 == demBallDropNum)
         {
            return 4;
         }
         if(name0 == dayLoveAdd)
         {
            return 40;
         }
         return 0;
      }
      
      public static function getMoreAddMul(name0:String) : Number
      {
         if(Boolean(name0.indexOf("DropPro")))
         {
            return 0.5;
         }
         return 1;
      }
   }
}

