package dataAll.body.attack
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll.image.ImageUrlDefine;
   import flash.geom.Rectangle;
   
   public class BodyAttackDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var cn:String = "";
      
      public var imgLabel:String = "";
      
      public var keyName:String = "";
      
      public var cd:Number = 0;
      
      public var bulletLabel:String = "";
      
      public var bulletArr:Array = [];
      
      public var skillArr:Array = [];
      
      public var grapRect:Rectangle = null;
      
      public var mustGrapRectB:Boolean = false;
      
      public var exactGrapRectB:Boolean = false;
      
      public var grapMaxLen:int = -1;
      
      public var grapMinLen:int = -1;
      
      public var oneHurtB:Boolean = false;
      
      public var noAiChooseB:Boolean = false;
      
      public var noShootB:Boolean = false;
      
      public var continuousNum:int = 0;
      
      public var noUseOtherSkillB:Boolean = false;
      
      public var ingfollowB:Boolean = false;
      
      public var hurtRatio:Number = 1;
      
      public var hurtMul:Number = 1;
      
      public var transBackMul:Number = 1;
      
      public var attackType:String = "physics";
      
      public var hitImg:ImageUrlDefine = new ImageUrlDefine();
      
      public var hitMaxNum:int = 999;
      
      public var shakeValue:Number = 0;
      
      public var screenShakeValue:Number = 0;
      
      public var beatBack:Number = 0;
      
      public var meBack:Number = 0;
      
      public function BodyAttackDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         this.cd = int(String(xml0.@cd));
         this.cn = String(xml0.cn);
         this.imgLabel = String(xml0.imgLabel);
         this.keyName = String(xml0.keyName);
         this.bulletLabel = String(xml0.bulletLabel);
         if(String(xml0.bulletArr) != "")
         {
            this.bulletArr = String(xml0.bulletArr).split(",");
         }
         if(String(xml0.skillArr) == "")
         {
            this.skillArr = [];
         }
         else
         {
            this.skillArr = String(xml0.skillArr).split(",");
         }
         this.oneHurtB = Boolean(int(xml0.oneHurtB));
         this.mustGrapRectB = Boolean(int(xml0.mustGrapRectB));
         this.exactGrapRectB = Boolean(int(xml0.exactGrapRectB));
         this.noUseOtherSkillB = Boolean(int(xml0.noUseOtherSkillB));
         this.ingfollowB = Boolean(int(xml0.ingfollowB));
         this.grapRect = ComMethod.getRect(xml0.grapRect);
         if(String(xml0.grapMaxLen) != "")
         {
            this.grapMaxLen = int(xml0.grapMaxLen);
         }
         if(String(xml0.grapMinLen) != "")
         {
            this.grapMinLen = int(xml0.grapMinLen);
         }
         this.hurtRatio = Number(xml0.hurtRatio);
         if(this.hurtRatio == 0)
         {
            this.hurtRatio = 1;
         }
         this.hurtMul = Number(xml0.hurtMul);
         this.attackType = String(xml0.attackType);
         if(this.attackType == "")
         {
            this.attackType = "physics";
         }
         this.noAiChooseB = Boolean(int(String(xml0.noAiChooseB)));
         this.noShootB = Boolean(int(String(xml0.noShootB)));
         this.shakeValue = int(xml0.shakeValue);
         this.continuousNum = int(xml0.continuousNum);
         this.screenShakeValue = int(xml0.screenShakeValue);
         this.beatBack = int(xml0.beatBack);
         this.meBack = int(xml0.meBack);
         this.hitImg.inData_byXML(xml0.hitImgUrl[0]);
         this.transBackMul = Number(xml0.transBackMul);
         if(String(xml0.transBackMul) == "")
         {
            this.transBackMul = 1;
         }
      }
      
      public function getHurtData() : HurtData
      {
         var h0:HurtData = new HurtData();
         h0.hurtRatio = this.hurtRatio;
         h0.hurtMul = this.hurtMul;
         h0.attackType = this.attackType;
         h0.screenShakeValue = this.screenShakeValue;
         h0.shakeValue = this.shakeValue;
         h0.beatBack = this.beatBack;
         h0.hitImg = this.hitImg;
         h0.from = HurtData.FROM_ATTACK;
         h0.fromAttack = this;
         return h0;
      }
      
      public function getBulletLabelArr() : Array
      {
         var arr0:Array = this.bulletArr.concat([]);
         if(this.bulletLabel != "" && this.bulletLabel != "no")
         {
            arr0.push(this.bulletLabel);
         }
         return arr0;
      }
      
      public function getAllImageUrl() : Array
      {
         var url0:ImageUrlDefine = null;
         var bulletArr0:Array = null;
         var bulletLabel0:String = null;
         var arr0:Array = [];
         var imgArr0:Array = [this.hitImg];
         for each(url0 in imgArr0)
         {
            if(url0.url != "")
            {
               ComMethod.addNoRepeatInArr(arr0,url0.url);
            }
         }
         bulletArr0 = this.getBulletLabelArr();
         for each(bulletLabel0 in bulletArr0)
         {
            ComMethod.addNoRepeatArrInArr(arr0,Gaming.defineGroup.bullet.getDefine(bulletLabel0).getAllImageUrl());
         }
         return arr0;
      }
   }
}

