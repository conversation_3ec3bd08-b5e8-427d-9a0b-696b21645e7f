package dataAll.equip.save
{
   import com.sounto.utils.ClassProperty;
   
   public class FashionSave extends EquipSave
   {
      
      public static var pro_arr:Array = null;
      
      public var showHero:String = "";
      
      public function FashionSave()
      {
         super();
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         super.inData_byObj(obj0);
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
   }
}

