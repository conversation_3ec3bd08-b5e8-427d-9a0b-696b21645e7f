package dataAll._app.active
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class AccActiveSave
   {
      
      public static var pro_arr:Array = null;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var dayAddB:Boolean = false;
      
      public var giftB:Boolean = false;
      
      public function AccActiveSave()
      {
         super();
      }
      
      public static function getDayBeforeStr() : String
      {
         return "在10月15日之前";
      }
      
      public static function getActivityGiftStr() : String
      {
         return "“大圣限时皮肤”";
      }
      
      public static function getActivityGiftGroup() : GiftAddDefineGroup
      {
         var g0:GiftAddDefineGroup = null;
         g0 = new GiftAddDefineGroup();
         g0.cnName = "活跃值礼包";
         g0.addGiftByStr("equip;greatSage2;1");
         return g0;
      }
      
      public static function getActivityMustMax() : int
      {
         return 90;
      }
      
      public static function getActivityDayMax() : int
      {
         return 8;
      }
      
      public function get day() : Number
      {
         return this.CF.getAttribute("day");
      }
      
      public function set day(v0:Number) : void
      {
         this.CF.setAttribute("day",v0);
      }
      
      public function get giftNum() : Number
      {
         return this.CF.getAttribute("giftNum");
      }
      
      public function set giftNum(v0:Number) : void
      {
         this.CF.setAttribute("giftNum",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function newDayCtrl() : void
      {
         this.dayAddB = false;
      }
      
      public function activityPan(nowActive0:Number) : void
      {
         if(!this.dayAddB)
         {
            if(nowActive0 >= getActivityMustMax())
            {
               this.dayAddB = true;
               ++this.day;
            }
         }
      }
   }
}

