package com.sounto.math
{
   import flash.geom.Point;
   
   public class Maths
   {
      
      private static var temp_p0:Point = new Point();
      
      public static var pi:Number = Math.PI;
      
      public function Maths()
      {
         super();
      }
      
      public static function Pn(xx:Number) : Number
      {
         if(xx == 0)
         {
            return 0;
         }
         if(xx < 0)
         {
            return -1;
         }
         return 1;
      }
      
      public static function PanSame(n1:Number, n2:Number) : Boolean
      {
         if(Pn(n1) == Pn(n2))
         {
            return true;
         }
         return false;
      }
      
      public static function Pow(xx:Number, yy:Number) : Number
      {
         return Math.pow(xx,yy);
      }
      
      public static function Abs(xx:Number) : Number
      {
         return Math.abs(xx);
      }
      
      public static function Abs2(xx:Number) : Number
      {
         return (xx ^ xx >> 31) - (xx >> 31);
      }
      
      public static function Ra(an:Number) : Number
      {
         return an * 180 / Math.PI;
      }
      
      public static function An(ra:Number) : Number
      {
         return ra / 180 * Math.PI;
      }
      
      public static function Tan2(yy:Number, xx:Number) : Number
      {
         return Math.atan2(yy,xx);
      }
      
      public static function Long(xx:Number, yy:Number) : Number
      {
         return Math.sqrt(xx * xx + yy * yy);
      }
      
      public static function Any(vy:Number, dd:Number) : Number
      {
         return vy * Math.sin(dd);
      }
      
      public static function Anx(vx:Number, dd:Number) : Number
      {
         return vx * Math.cos(dd);
      }
      
      public static function Vi(v1:Number, d1:Number, v2:Number, d2:Number) : Object
      {
         var newo:Object = new Object();
         var vx:* = Anx(v1,d1) + Anx(v2,d2);
         var vy:* = Any(v1,d1) + Any(v2,d2);
         newo.vx = vx;
         newo.vy = vy;
         newo.d0 = Tan2(vy,vx);
         newo.v0 = Long(vx,vy);
         return newo;
      }
      
      public static function FengJ(v:Number, d:Number, dc:Number) : Object
      {
         var newo:Object = new Object();
         var vc:* = v * Math.cos(d - dc);
         newo.vc_fang = vc;
         newo.dc_fang = dc;
         newo.dc = ZhunV(vc,dc);
         newo.vc = Abs(vc);
         newo.de = ZhunJ(dc + LineVsJ(d,dc) * pi / 2);
         newo.ve = Abs(v * Math.sin(d - dc));
         return newo;
      }
      
      public static function ZhunJ(d:Number) : Number
      {
         return (d + pi + 1000 * pi) % (2 * pi) - pi;
      }
      
      public static function ZhunV(v:Number, d:Number) : Number
      {
         if(v >= 0)
         {
            return d;
         }
         return ZhunJ(d + pi);
      }
      
      public static function flipRa_Y(ra:Number) : *
      {
         return -ra - Math.PI;
      }
      
      public static function flipRa(ra:Number, ra0:Number) : *
      {
         return ra - (ra - ra0) * 2;
      }
      
      public static function LineVsJ(d:Number, ld:Number) : int
      {
         var lj:* = Math.sin(d - ld);
         return Pn(lj);
      }
      
      public static function J_J(d1:Number, d2:Number) : Number
      {
         var jj:* = J_J2(d1,d2);
         if(jj > pi)
         {
            return 2 * pi - jj;
         }
         return jj;
      }
      
      public static function J_J_Ra(d1:Number, d2:Number) : Number
      {
         var jj:* = J_J2(An(d1),An(d2));
         if(jj > pi)
         {
            return Ra(2 * pi - jj);
         }
         return Ra(jj);
      }
      
      public static function J_J2(d1:Number, d2:Number) : Number
      {
         return ZhunJ2(d1 - d2);
      }
      
      public static function ZhunJ2(d:Number) : Number
      {
         return (d + 1000 * pi) % (2 * pi);
      }
      
      public static function compare_Y(d:Number) : Boolean
      {
         d = ZhunJ2(d);
         if(d < Math.PI / 2 || d > Math.PI / 2 * 3)
         {
            return true;
         }
         return false;
      }
      
      public static function dealRaByRange(ra0:Number, min0:Number, max0:Number) : Number
      {
         var limitB0:Boolean = !raInRange(ra0,min0,max0);
         if(limitB0)
         {
            if(J_J(ra0,min0) < J_J(ra0,max0))
            {
               ra0 = min0;
            }
            else
            {
               ra0 = max0;
            }
         }
         return ra0;
      }
      
      public static function raInRange(ra0:Number, min0:Number, max0:Number) : Boolean
      {
         var limitB0:Boolean = false;
         if(max0 * min0 > 0)
         {
            if(max0 < min0)
            {
               if(ra0 <= min0 && ra0 >= max0)
               {
                  limitB0 = true;
               }
            }
            else if(!(ra0 >= min0 && ra0 <= max0))
            {
               limitB0 = true;
            }
         }
         else if(max0 < min0)
         {
            if(ra0 <= min0 && ra0 >= max0)
            {
               limitB0 = true;
            }
         }
         else if(ra0 <= min0 || ra0 >= max0)
         {
            limitB0 = true;
         }
         return !limitB0;
      }
      
      public static function cutLine(x0:Number, y0:Number, x1:Number, y1:Number, len0:Number) : Point
      {
         var len2:Number = NaN;
         temp_p0.x = x1;
         temp_p0.y = y1;
         if(!(x0 == x1 && y0 == y1))
         {
            if(x0 == x1)
            {
               if(y0 > y1)
               {
                  temp_p0.y -= len0;
               }
               else
               {
                  temp_p0.y += len0;
               }
            }
            else if(y0 == y1)
            {
               if(x0 > x1)
               {
                  temp_p0.x -= len0;
               }
               else
               {
                  temp_p0.x += len0;
               }
            }
            else
            {
               len2 = Math.sqrt((x0 - x1) * (x0 - x1) + (y0 - y1) * (y0 - y1));
               temp_p0.x = (x1 - x0) * (len2 - len0) / len2 + x0;
               temp_p0.y = (y1 - y0) * (len2 - len0) / len2 + y0;
            }
         }
         return temp_p0;
      }
      
      public static function closeToMax(v0:Number, start0:Number, max0:Number, mul0:Number = 0.95) : Number
      {
         if(v0 <= start0)
         {
            return v0;
         }
         return start0 + (max0 - start0) * (1 - Math.pow(mul0,v0 - start0 + 1));
      }
   }
}

