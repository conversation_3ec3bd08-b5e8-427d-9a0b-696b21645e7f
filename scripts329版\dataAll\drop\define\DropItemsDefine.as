package dataAll.drop.define
{
   import com.sounto.utils.ClassProperty;
   
   public class DropItemsDefine
   {
      
      public static const TYPE_ARMS:String = "arms";
      
      public static const TYPE_EQUIP:String = "equip";
      
      public static const TYPE_GENE:String = "gene";
      
      public static const TYPE_THINGS:String = "things";
      
      public static const TYPE_PARTS:String = "parts";
      
      public static const TYPE_EFFECT:String = "effect";
      
      public static const TYPE_FOOD:String = "food";
      
      public static const TYPE_BOSS_CARD:String = "bossCard";
      
      public static const NAME_addLifeMul:String = "addLifeMul";
      
      public static var pro_arr:Array = [];
      
      public var type:String = "";
      
      public var secType:String = "";
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var text:String = "";
      
      public var imgUrl:String = "";
      
      public var extraSkill:String = "";
      
      public var lifetime:Number = -1;
      
      public var noFollowHeroB:Boolean = false;
      
      public var smallMapColor:String = "";
      
      public var lightImg:String = "";
      
      public function DropItemsDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML, father0:String) : void
      {
         ClassProperty.inData_byXML(this,xml0,pro_arr);
         this.type = father0;
      }
   }
}

