package dataAll.level.define.unit
{
   import com.sounto.utils.ClassProperty;
   import dataAll.level.define.LevelDefineGroup;
   
   public class UnitOrderDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var id:String = "";
      
      public var camp:String = "enemy";
      
      public var numberType:String = "number";
      
      public var defaultUnit:OneUnitOrderDefine;
      
      public var arr:Array = [];
      
      public var haveSuperOrBossB:Boolean = false;
      
      public var haveBossB:Boolean = false;
      
      public var haveWeUnitB:Boolean = false;
      
      public var mustSuperB:Boolean = false;
      
      public function UnitOrderDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML, default_d0:OneUnitOrderDefine, dg0:UnitOrderDefineGroup) : void
      {
         var n:* = undefined;
         var xml2:XML = null;
         var d0:OneUnitOrderDefine = null;
         this.id = xml0.@id;
         this.camp = xml0.@camp;
         if(this.camp == "")
         {
            this.camp = "enemy";
         }
         this.numberType = String(xml0.numberType);
         if(this.numberType == "")
         {
            this.numberType = "number";
         }
         this.mustSuperB = Boolean(int(xml0.@mustSuperB));
         if(xml0.§default§.length() > 0)
         {
            this.defaultUnit = new OneUnitOrderDefine();
            this.defaultUnit.inData_byXML(xml0.§default§[0],this.camp);
         }
         var xmllist2:XMLList = xml0.unit;
         for(n in xmllist2)
         {
            xml2 = xmllist2[n];
            d0 = new OneUnitOrderDefine();
            d0.inData_byXML(xml2,this.camp);
            this.addOneUnitOrderDefine(d0,default_d0);
         }
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function clone() : UnitOrderDefine
      {
         var d0:UnitOrderDefine = new UnitOrderDefine();
         d0.inData_byObj(this);
         return d0;
      }
      
      public function addOneUnitOrderDefine(d0:OneUnitOrderDefine, default_d0:OneUnitOrderDefine) : void
      {
         if(this.camp == "enemy")
         {
            d0.mergeData_byDefault(default_d0);
         }
         if(Boolean(this.defaultUnit))
         {
            d0.mergeData_byDefault(this.defaultUnit);
         }
         d0.defaultDeal();
         this.arr.push(d0);
         if(d0.unitType != "normal")
         {
            this.haveSuperOrBossB = true;
         }
         if(d0.unitType == UnitType.BOSS)
         {
            this.haveBossB = true;
            if(d0.camp == "enemy")
            {
               LevelDefineGroup.addBossDefine(d0);
            }
         }
         if(d0.camp == "we")
         {
            this.haveWeUnitB = true;
         }
      }
      
      public function getNumArr(enemyNormalMul:Number = 1) : Array
      {
         var n:* = undefined;
         var d0:OneUnitOrderDefine = null;
         var arr0:Array = [];
         for(n in this.arr)
         {
            d0 = this.arr[n];
            arr0.push(d0.getNum(enemyNormalMul));
         }
         return arr0;
      }
      
      public function getNumSum(enemyNormalMul:Number = 1) : int
      {
         var n:* = undefined;
         var d0:OneUnitOrderDefine = null;
         var num0:int = 0;
         for(n in this.arr)
         {
            d0 = this.arr[n];
            num0 += d0.getNum(enemyNormalMul);
         }
         return num0;
      }
      
      public function fleshBodyName() : void
      {
         var n:* = undefined;
         var d0:OneUnitOrderDefine = null;
         for(n in this.arr)
         {
            d0 = this.arr[n];
            d0.fleshBodyName();
         }
      }
   }
}

