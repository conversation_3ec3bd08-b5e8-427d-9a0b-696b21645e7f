package dataAll._app.arena.record
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.oldUtils.Sounto64;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.top.TopBarSave;
   
   public class OneArenaRecordSave
   {
      
      public static var pro_arr:Array = [];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var topSave:TopBarSave = new TopBarSave();
      
      public var playerName:String = "";
      
      private var _killNum:String = "";
      
      private var _dieNum:String = "";
      
      public var killTime:String = "";
      
      public var dieTime:String = "";
      
      public var killSortIndex:Number = 0;
      
      public var dieSortIndex:Number = 0;
      
      public var clearHtml:int = 0;
      
      public function OneArenaRecordSave()
      {
         super();
         this.score = 0;
      }
      
      public function get score() : Number
      {
         return this.CF.getAttribute("score");
      }
      
      public function set score(v0:Number) : void
      {
         this.CF.setAttribute("score",v0);
      }
      
      public function set killNum(v0:Number) : void
      {
         this._killNum = Sounto64.encode(String(v0));
      }
      
      public function get killNum() : Number
      {
         return Number(Sounto64.decode(this._killNum));
      }
      
      public function set dieNum(v0:Number) : void
      {
         this._dieNum = Sounto64.encode(String(v0));
      }
      
      public function get dieNum() : Number
      {
         return Number(Sounto64.decode(this._dieNum));
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         if(this.clearHtml == 0)
         {
            this.playerName = TextWay.clearHtmlNo(this.playerName);
            this.clearHtml = 1;
         }
      }
      
      public function inData(s0:TopBarSave, playerName0:String) : void
      {
         this.topSave = s0;
         this.playerName = TextWay.clearHtmlNo(playerName0);
      }
      
      public function addNum(winB0:Boolean, time0:String, sortIndex0:Number, score0:int, normalB0:Boolean) : void
      {
         if(normalB0 || !this.nowIsLastTimeB(time0))
         {
            this.score = score0;
         }
         if(winB0)
         {
            ++this.killNum;
            this.killTime = time0;
            this.killSortIndex = sortIndex0;
         }
         else
         {
            ++this.dieNum;
            this.dieTime = time0;
            this.dieSortIndex = sortIndex0;
         }
      }
      
      public function getMaxSortIndex() : Number
      {
         return this.killSortIndex > this.dieSortIndex ? this.killSortIndex : this.dieSortIndex;
      }
      
      public function getLastTimeState() : StringDate
      {
         var s2:StringDate = null;
         var s0:StringDate = new StringDate();
         if(this.killTime == "" && this.dieTime == "")
         {
            return null;
         }
         if(this.killTime != "" && this.dieTime != "")
         {
            s0.inData_byStr(this.killTime);
            s2 = new StringDate();
            s2.inData_byStr(this.dieTime);
            if(s0.compareDateValue(s2) > 0)
            {
               return s2;
            }
            return s0;
         }
         if(this.dieTime != "")
         {
            s0.inData_byStr(this.dieTime);
            return s0;
         }
         s0.inData_byStr(this.killTime);
         return s0;
      }
      
      public function nowIsLastTimeB(time0:String) : Boolean
      {
         var nowS0:StringDate = null;
         var s0:StringDate = this.getLastTimeState();
         if(Boolean(s0))
         {
            nowS0 = new StringDate();
            nowS0.inData_byStr(time0);
            return nowS0.reductionOne(s0) == 0;
         }
         return false;
      }
   }
}

