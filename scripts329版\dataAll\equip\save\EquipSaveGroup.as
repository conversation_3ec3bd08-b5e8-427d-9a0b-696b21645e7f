package dataAll.equip.save
{
   import com.sounto.utils.ClassProperty;
   import dataAll.items.save.ItemsSaveGroup;
   
   public class EquipSaveGroup extends ItemsSaveGroup
   {
      
      public static var pro_arr:Array = [];
      
      public var showFashionB:Boolean = true;
      
      public function EquipSaveGroup()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         super.inData_byObjAndClass(obj0,this.classPan);
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function clone() : EquipSaveGroup
      {
         var obj0:Object = ClassProperty.copyObj(this);
         var sg0:EquipSaveGroup = new EquipSaveGroup();
         sg0.inData_byObj(obj0);
         return sg0;
      }
      
      private function classPan(dataObj0:Object) : Class
      {
         if(dataObj0.hasOwnProperty("partType"))
         {
            return EquipSave.getSaveClass(dataObj0["partType"]);
         }
         return EquipSave;
      }
      
      override public function replaceItemsName(before0:String, after0:String) : void
      {
         var s0:EquipSave = null;
         for each(s0 in arr)
         {
            if(s0.name == before0)
            {
               s0.name = after0;
               s0.imgName = after0;
            }
         }
      }
   }
}

