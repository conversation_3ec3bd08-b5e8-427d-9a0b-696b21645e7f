package dataAll.level.define.unit
{
   import com.sounto.utils.ClassProperty;
   import dataAll.body.define.BodyCamp;
   import dataAll.body.define.NormalBodyDefine;
   
   public class OneUnitOrderDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var id:String = "";
      
      public var camp:String = "enemy";
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var num:Number = 0;
      
      public var unitType:String = "normal";
      
      public var isTrueBossB:Boolean = false;
      
      public var levelSetting:String = "";
      
      public var aiOrder:String = "";
      
      public var eacapeB:Boolean = false;
      
      public var warningRange:int = 0;
      
      public var skillCloseB:Boolean = false;
      
      public var skillArr:Array = [];
      
      public var noSuperB:Boolean = false;
      
      public var imgType:String = "bmp";
      
      public var noUnderHurtB:Boolean = false;
      
      public var extraTaskB:Boolean = false;
      
      public var avtiveSkillCdOverT:Number = -1;
      
      public var lifeMul:Number = 0;
      
      public var dpsMul:Number = 0;
      
      public var expMul:Number = 0;
      
      public var coinMul:Number = 0;
      
      public var headHurtMul:Number = -1;
      
      public var dpsSpecielLabel:String = "";
      
      public var dropLabel:String = "";
      
      public var dieGotoState:String = "";
      
      public var lastB:Boolean = false;
      
      public var armsRange:Array = [];
      
      public function OneUnitOrderDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML, camp0:String) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         this.camp = camp0;
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function clone() : OneUnitOrderDefine
      {
         var d0:OneUnitOrderDefine = new OneUnitOrderDefine();
         d0.inData_byObj(this);
         return d0;
      }
      
      public function defaultDeal() : void
      {
         if(this.aiOrder == "")
         {
            this.aiOrder = AiType.PATROL;
         }
         if(this.unitType == "")
         {
            this.unitType = UnitType.NORMAL;
         }
         if(this.num == 0)
         {
            this.num = 1;
         }
         if(this.lifeMul == 0)
         {
            this.lifeMul = 1;
         }
         if(this.dpsMul == 0)
         {
            this.dpsMul = 1;
         }
         if(this.expMul == 0)
         {
            this.expMul = 1;
         }
         if(this.coinMul == 0)
         {
            this.coinMul = 1;
         }
      }
      
      public function setAiFollowBody(id0:String) : void
      {
         this.aiOrder = "followBodyAttack:" + id0;
      }
      
      public function getNum(normalEnemyMul0:Number = 1) : int
      {
         var v0:int = this.num;
         if(normalEnemyMul0 > 1 && this.unitType == UnitType.NORMAL && this.camp == BodyCamp.ENEMY)
         {
            v0 = Math.round(v0 * normalEnemyMul0);
         }
         return v0;
      }
      
      public function mergeData_byDefault(d0:OneUnitOrderDefine) : *
      {
         var n:* = undefined;
         var pro0:String = null;
         for(n in pro_arr)
         {
            pro0 = pro_arr[n];
            if(this[pro0] is String)
            {
               if(this[pro0] == "" && d0[pro0] != "")
               {
                  this[pro0] = d0[pro0];
               }
            }
            else if(this[pro0] is Number)
            {
               if(this[pro0] == 0 && d0[pro0] != 0)
               {
                  this[pro0] = d0[pro0];
               }
            }
            else if(this[pro0] is Boolean)
            {
               if(this[pro0] == false && d0[pro0] != false)
               {
                  this[pro0] = d0[pro0];
               }
            }
         }
      }
      
      public function fleshBodyName() : void
      {
         var d0:NormalBodyDefine = null;
         if(this.name == "")
         {
            d0 = Gaming.defineGroup.body.getCnDefine(this.cnName);
            this.name = d0.name;
         }
         else
         {
            d0 = this.getBodyDefine();
            this.cnName = d0.cnName;
         }
      }
      
      public function getBodyDefine() : NormalBodyDefine
      {
         return Gaming.defineGroup.body.getDefine(this.name);
      }
   }
}

