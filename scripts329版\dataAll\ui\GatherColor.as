package dataAll.ui
{
   import dataAll._app.edit.list.EditListAgent;
   import dataAll.equip.define.EquipColor;
   
   public class GatherColor
   {
      
      public static const zero:String = "zero";
      
      public static var orange:String = "orange";
      
      public static var orangeness:String = "orangeness";
      
      public static var red:String = "red";
      
      public static var redness:String = "redness";
      
      public static var redness2:String = "redness2";
      
      public static var yellow:String = "yellow";
      
      public static var green:String = "green";
      
      public static var greeness:String = "greeness";
      
      public static const greenDark:String = "greenDark";
      
      public static var gray:String = "gray";
      
      public static var graydark:String = "graydark";
      
      public static var gray2:String = "gray2";
      
      public static var purple:String = "purple";
      
      public static var purpleness:String = "purpleness";
      
      public static var blove:String = "blove";
      
      public static var blueness:String = "blueness";
      
      public static var blue:String = "blue";
      
      public static var white:String = "white";
      
      public static var tipWhite:String = "tipWhite";
      
      public static var black:String = "black";
      
      public static var darkgold:String = EquipColor.DARKGOLD;
      
      public static var purgold:String = EquipColor.PURGOLD;
      
      public static var yagold:String = EquipColor.YAGOLD;
      
      public static var Color:String = "#CCCCCC";
      
      public static var orangeColor:String = "#FF6600";
      
      public static var orangenessColor:String = "#FF9900";
      
      public static var redColor:String = "#FF0000";
      
      public static var rednessColor:String = "#FF6666";
      
      public static var redness2Color:String = "#FF9999";
      
      public static var yellowColor:String = "#FFFF00";
      
      public static var greenColor:String = "#00FF00";
      
      public static var greenessColor:String = "#66FF00";
      
      private static const greenDarkColor:String = "00CC00";
      
      public static var grayColor:String = "#CCCCCC";
      
      public static var graydarkColor:String = "#666666";
      
      public static var gray2Color:String = "#888888";
      
      public static var purpleColor:String = "#FF66FF";
      
      public static var purplenessColor:String = "#FF99FF";
      
      public static var bloveColor:String = "#00CCFF";
      
      public static var bluenessColor:String = "#66FFFF";
      
      public static var blueColor:String = "#00FFFF";
      
      public static var whiteColor:String = "#FFFFFF";
      
      public static var tipWhiteColor:String = "#CCCCCC";
      
      public static var blackColor:String = "#7979BF";
      
      public static var darkgoldColor:String = EquipColor.darkgoldHtmlColor;
      
      public static var purgoldColor:String = EquipColor.purgoldHtmlColor;
      
      public static var yagoldColor:String = EquipColor.yagoldHtmlColor;
      
      public static var arr:Array = [greenDark,orange,orangeness,red,redness,redness2,yellow,green,greeness,gray,graydark,gray2,purple,purpleness,blove,blueness,blue,white,tipWhite,black,darkgold,purgold,yagold];
      
      private static var editAgent:EditListAgent = null;
      
      public function GatherColor()
      {
         super();
      }
      
      public static function getColor(name0:String) : String
      {
         return GatherColor[name0 + "Color"];
      }
      
      public static function getUintColor(name0:String) : String
      {
         var color0:String = getColor(name0);
         return "0x" + color0.substr(1);
      }
      
      public static function uintToHtml(name0:String) : String
      {
         return "#" + name0.substr(2);
      }
      
      public static function getColorListAgent(nowColor0:String) : EditListAgent
      {
         var C_ARR:Array = null;
         var gap0:int = 0;
         var fatherArr0:Array = null;
         var cnArr0:Array = null;
         var i:int = 0;
         var r:int = 0;
         var g:int = 0;
         var b:int = 0;
         var r0:String = null;
         var g0:String = null;
         var b0:String = null;
         var color0:String = null;
         var name0:String = null;
         var father0:String = null;
         var a0:EditListAgent = editAgent;
         if(Boolean(a0))
         {
            a0.clearFun();
         }
         else
         {
            a0 = new EditListAgent();
            C_ARR = ["0","3","6","9","C","F"];
            gap0 = int(C_ARR.length);
            fatherArr0 = [];
            cnArr0 = [];
            for(i = 0; i < gap0; i++)
            {
               fatherArr0.push("g" + i);
               cnArr0.push("组" + (i + 1));
            }
            fatherArr0.unshift("normal");
            cnArr0.unshift("常用");
            a0.inTitle(fatherArr0,cnArr0);
            for(r = 0; r < gap0; r++)
            {
               for(g = 0; g < gap0; g++)
               {
                  for(b = 0; b < gap0; b++)
                  {
                     r0 = C_ARR[r];
                     g0 = C_ARR[g];
                     b0 = C_ARR[b];
                     color0 = r0 + r0 + g0 + g0 + b0 + b0;
                     name0 = "0x" + color0;
                     father0 = "g" + r;
                     a0.addNormalLast(name0,name0,father0,"#" + color0);
                  }
               }
            }
            a0.addNormalLast(zero,"空颜色","normal");
            inAgentNormalColor(a0,"normal");
         }
         if(a0.getDataByName(nowColor0) == null)
         {
            a0.addNormalLast(nowColor0,nowColor0,"normal",uintToHtml(nowColor0));
         }
         a0.setNoLinkArr([nowColor0]);
         a0.createAllText();
         return a0;
      }
      
      private static function inAgentNormalColor(a0:EditListAgent, father0:String) : void
      {
         var name0:String = null;
         var color0:String = null;
         var dname0:String = null;
         for each(name0 in arr)
         {
            color0 = getColor(name0);
            dname0 = "0x" + color0.substr(1);
            a0.addNormalLast(dname0,dname0,father0,color0);
         }
      }
   }
}

