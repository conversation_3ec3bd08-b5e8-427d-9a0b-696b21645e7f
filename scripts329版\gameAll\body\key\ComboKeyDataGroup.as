package gameAll.body.key
{
   import com.sounto.key.Keys;
   import com.sounto.key.KeysState;
   import dataAll._app.setting.key.SettingKeySave;
   
   public class ComboKeyDataGroup
   {
      
      private var MIN_T:Number = 0.5;
      
      private var codeArr:Array = [];
      
      private var dataArr:Array = [];
      
      private var beforeT:Number = 0;
      
      public function ComboKeyDataGroup()
      {
         super();
      }
      
      public static function getDataArrByComboStr(str0:String, keySave0:SettingKeySave) : Array
      {
         var s0:String = null;
         var da0:ComboKeyData = null;
         var arr0:Array = [];
         var strArr0:Array = str0.split("_");
         for each(s0 in strArr0)
         {
            da0 = new ComboKeyData();
            da0.inComboStr(s0,keySave0);
            arr0.push(da0);
         }
         return arr0;
      }
      
      public function keyPan(arr0:Array, comboKeyArr0:Array, keySave0:SettingKeySave) : Boolean
      {
         var name0:String = null;
         var code0:int = 0;
         var key0:Keys = null;
         var addB0:Boolean = false;
         for each(name0 in comboKeyArr0)
         {
            code0 = keySave0.getCode(name0);
            key0 = arr0[code0];
            if(key0.s == KeysState.DOWN)
            {
               this.addKey(key0,name0);
               addB0 = true;
            }
         }
         return addB0;
      }
      
      public function addKey(k0:Keys, label0:String) : void
      {
         var da0:ComboKeyData = new ComboKeyData();
         da0.code = k0.code;
         da0.s = k0.s;
         da0.gap = this.beforeT;
         da0.label = label0;
         this.beforeT = 0;
         this.dataArr.push(da0);
         if(this.dataArr.length > 10)
         {
            this.dataArr.shift();
         }
      }
      
      public function clear() : void
      {
         this.dataArr.length = 0;
         this.beforeT = 0;
      }
      
      private function getLastLabelArr(num0:int) : Array
      {
         var da0:ComboKeyData = null;
         var arr0:Array = [];
         var len0:int = int(this.dataArr.length);
         for(var i:int = len0 - num0; i < len0; i++)
         {
            da0 = this.dataArr[i];
            if(da0 is ComboKeyData)
            {
               arr0.push(da0.label);
            }
         }
         return arr0;
      }
      
      public function haveComboB() : Boolean
      {
         return this.dataArr.length >= 2;
      }
      
      public function panCombo(dataArr0:Array, keySave0:SettingKeySave) : Boolean
      {
         var da0:ComboKeyData = null;
         var c0:ComboKeyData = null;
         var len0:int = int(dataArr0.length);
         for(var n:int = len0 - 1; n >= 0; n--)
         {
            da0 = dataArr0[n];
            c0 = this.dataArr[this.dataArr.length + n - len0];
            if(!(c0 is ComboKeyData))
            {
               return false;
            }
            if(keySave0 is SettingKeySave)
            {
               da0.code = keySave0.getCode(da0.label);
            }
            if(!(c0.code == da0.code && c0.s == da0.s))
            {
               return false;
            }
         }
         return true;
      }
      
      public function panComboLabelArr(labelArr0:Array) : Boolean
      {
         var nArr0:Array = this.getLastLabelArr(labelArr0.length);
         var arr_len0:int = int(labelArr0.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            if(labelArr0[i] != nArr0[i])
            {
               return false;
            }
         }
         return true;
      }
      
      public function panComboLabelArrRandom(labelArr0:Array) : Boolean
      {
         var name0:String = null;
         var nArr0:Array = this.getLastLabelArr(labelArr0.length);
         if(nArr0.length > 0)
         {
            for each(name0 in labelArr0)
            {
               if(nArr0.indexOf(name0) == -1)
               {
                  return false;
               }
            }
            return true;
         }
         return false;
      }
      
      public function FTimer() : void
      {
         this.beforeT += 1 / 30;
         if(this.beforeT > this.MIN_T)
         {
            this.clear();
         }
      }
      
      public function toString() : String
      {
         return this.dataArr.toString();
      }
   }
}

